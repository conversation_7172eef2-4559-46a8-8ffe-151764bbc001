import * as employeeController from '@controllers/employeeController'
import {
  jwtAdminAuthMiddleware,
  jwtHeaderAuthMiddleware,
} from '@middleware/jwtAuthMiddleware'
import { Router } from 'express'

export const employeeRouter = Router()

// GET /api/v1/employees/me - Get current authenticated employee
employeeRouter.get(
  '/me',
  jwtHeaderAuthMiddleware,
  employeeController.getCurrentEmployee
)

// GET /api/v1/employees - Get all employees (admin only)
employeeRouter.get(
  '/',
  jwtHeaderAuthMiddleware,
  jwtAdminAuthMiddleware,
  employeeController.getAllEmployees
)

// POST /api/v1/employees/setup-oauth - Set up OAuth access for existing employee (self or admin)
employeeRouter.post(
  '/setup-oauth',
  jwtHeaderAuthMiddleware,
  employeeController.setupOAuthAccess
)

// PUT /api/v1/employees/:id - Update employee OAuth settings (self or admin)
employeeRouter.put(
  '/:id',
  jwtHeaderAuthMiddleware,
  employeeController.updateEmployee
)

// GET /api/v1/employees/:id - Get employee by ID (self or admin)
employeeRouter.get(
  '/:id',
  jwtHeaderAuthMiddleware,
  employeeController.getEmployeeById
)

// DELETE /api/v1/employees/:id - Remove OAuth access for employee (self or admin)
employeeRouter.delete(
  '/:id',
  jwtHeaderAuthMiddleware,
  employeeController.removeOAuthAccess
)

// POST /api/v1/employees/:id/change-password - Change employee password (self or admin)
employeeRouter.post(
  '/:id/change-password',
  jwtHeaderAuthMiddleware,
  employeeController.changePassword
)

// POST /api/v1/employees/reset-password - Reset password using reset code (no auth required)
employeeRouter.post('/reset-password', employeeController.resetPasswordWithCode)
