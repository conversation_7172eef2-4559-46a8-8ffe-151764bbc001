import { sql, type Kysely } from 'kysely'

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('auth.oauth_tokens')
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('id', 'serial', (col) => col.primaryKey())
    .addColumn('client_id', 'varchar(255)', (col) => col.notNull())
    .addColumn('employee_id', 'integer', (col) => col.notNull())
    .addColumn('access_token', 'varchar(500)', (col) => col.notNull().unique())
    .addColumn('expires_at', 'timestamp', (col) => col.notNull())
    .addColumn('is_revoked', 'boolean', (col) => col.notNull().defaultTo(false))
    .addColumn('refresh_token', 'varchar(500)', (col) => col)
    .addColumn('token_type', 'varchar(50)', (col) =>
      col.notNull().defaultTo('Bearer')
    )
    .addColumn('scope', 'text', (col) => col)
    .addColumn('authorization_code', 'varchar(255)', (col) => col)
    .addColumn('code_challenge', 'varchar(255)', (col) => col)
    .addColumn('code_challenge_method', 'varchar(10)', (col) => col)
    .addColumn('redirect_uri', 'text', (col) => col)
    .execute()

  // Add foreign key constraints
  await db.schema
    .alterTable('auth.oauth_tokens')
    .addForeignKeyConstraint(
      'fk_oauth_tokens_client_id',
      ['client_id'],
      'auth.oauth_clients',
      ['client_id']
    )
    .execute()

  await db.schema
    .alterTable('auth.oauth_tokens')
    .addForeignKeyConstraint(
      'fk_oauth_tokens_employee_id',
      ['employee_id'],
      'auth.employees',
      ['employee_id']
    )
    .execute()

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_oauth_tokens_access_token')
    .on('auth.oauth_tokens')
    .column('access_token')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_refresh_token')
    .on('auth.oauth_tokens')
    .column('refresh_token')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_client_id')
    .on('auth.oauth_tokens')
    .column('client_id')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_employee_id')
    .on('auth.oauth_tokens')
    .column('employee_id')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_authorization_code')
    .on('auth.oauth_tokens')
    .column('authorization_code')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_expires_at')
    .on('auth.oauth_tokens')
    .column('expires_at')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_is_revoked')
    .on('auth.oauth_tokens')
    .column('is_revoked')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('auth.oauth_tokens').execute()
}
