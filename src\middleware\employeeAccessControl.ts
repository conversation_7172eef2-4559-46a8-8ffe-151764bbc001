import { Request, Response, NextFunction } from 'express'
import { StatusCodes } from 'http-status-codes'
import { employeeService } from '@services/employeeService'

interface AuthenticatedRequest extends Request {
  employee?: {
    employee_id: number
    client_id: string
    scope?: string
    is_admin?: boolean
  }
}

interface AuthenticatedResponse extends Response {
  locals: {
    tokenPayload?: {
      employee_id: number
      client_id: string
      scope?: string
      employee?: {
        employee_id: number
        first_name?: string
        last_name?: string
        nickname?: string
        is_admin?: boolean
      }
    }
    employee?: {
      employee_id: number
      first_name?: string
      last_name?: string
      nickname?: string
      is_admin?: boolean
    }
  }
}

/**
 * Middleware to check if the authenticated employee can access the requested employee data
 * Rules:
 * - Employees can only access their own data (employee_id matches)
 * - Admin employees can access any employee data
 * - Must be used after JWT authentication middleware
 */
export const checkEmployeeAccess = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse,
  next: NextFunction
): Promise<void> => {
  try {
    // Get the authenticated employee from JWT token
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    const authenticatedEmployeeId = tokenPayload.employee_id

    // Get the target employee ID from the request
    let targetEmployeeId: number | null = null
    
    // Check if employee ID is in URL params (e.g., /employees/:id)
    if (req.params.id) {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid employee ID',
        })
        return
      }
      targetEmployeeId = id
    }
    
    // Check if employee ID is in request body (e.g., for setup operations)
    if (req.body.employee_id) {
      const id = parseInt(req.body.employee_id)
      if (isNaN(id)) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid employee ID in request body',
        })
        return
      }
      targetEmployeeId = id
    }

    // If no target employee ID found, this might be a general operation
    // that doesn't target a specific employee (like getCurrentEmployee)
    if (targetEmployeeId === null) {
      next()
      return
    }

    // Check if the authenticated employee is trying to access their own data
    if (authenticatedEmployeeId === targetEmployeeId) {
      next()
      return
    }

    // If not accessing own data, check if the authenticated employee is an admin
    const authenticatedEmployee = await employeeService.getEmployeeById(authenticatedEmployeeId)
    if (!authenticatedEmployee) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authenticated employee not found',
      })
      return
    }

    if (authenticatedEmployee.is_admin) {
      // Admin can access any employee data
      next()
      return
    }

    // Not admin and not accessing own data - forbidden
    res.status(StatusCodes.FORBIDDEN).json({
      error: 'forbidden',
      message: 'You can only access your own employee data. Admin access required for other employees.',
    })
  } catch (error) {
    console.error('Employee access control error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Access control check failed',
    })
  }
}

/**
 * Middleware to check if the authenticated employee is an admin
 * Must be used after JWT authentication middleware
 */
export const requireAdmin = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse,
  next: NextFunction
): Promise<void> => {
  try {
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check if admin flag is in the JWT token
    if (tokenPayload.employee?.is_admin) {
      next()
      return
    }

    // If not in JWT, check the database
    const employee = await employeeService.getEmployeeById(tokenPayload.employee_id)
    if (!employee) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Employee not found',
      })
      return
    }

    if (!employee.is_admin) {
      res.status(StatusCodes.FORBIDDEN).json({
        error: 'forbidden',
        message: 'Admin access required',
      })
      return
    }

    next()
  } catch (error) {
    console.error('Admin access control error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Admin access check failed',
    })
  }
}

/**
 * Middleware to check if the authenticated employee can perform the requested operation
 * For operations that should only be performed by the employee themselves or an admin
 */
export const checkSelfOrAdmin = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse,
  next: NextFunction
): Promise<void> => {
  try {
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    const authenticatedEmployeeId = tokenPayload.employee_id

    // Get target employee ID from params or body
    let targetEmployeeId: number | null = null
    if (req.params.id) {
      targetEmployeeId = parseInt(req.params.id)
    } else if (req.body.employee_id) {
      targetEmployeeId = parseInt(req.body.employee_id)
    }

    if (targetEmployeeId === null || isNaN(targetEmployeeId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Employee ID required',
      })
      return
    }

    // Allow if accessing own data
    if (authenticatedEmployeeId === targetEmployeeId) {
      next()
      return
    }

    // Check if admin
    const authenticatedEmployee = await employeeService.getEmployeeById(authenticatedEmployeeId)
    if (!authenticatedEmployee) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authenticated employee not found',
      })
      return
    }

    if (!authenticatedEmployee.is_admin) {
      res.status(StatusCodes.FORBIDDEN).json({
        error: 'forbidden',
        message: 'You can only perform this operation on your own account. Admin access required for other employees.',
      })
      return
    }

    next()
  } catch (error) {
    console.error('Self or admin access control error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Access control check failed',
    })
  }
}
