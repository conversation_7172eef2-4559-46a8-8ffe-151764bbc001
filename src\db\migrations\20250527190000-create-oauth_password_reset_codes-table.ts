import { Kysely, sql } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema
    .createTable('auth.password_reset_codes')
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('id', 'serial', (col) => col.primaryKey())
    .addColumn('employee_id', 'integer', (col) => col.notNull())
    .addColumn('reset_code', 'varchar(36)', (col) => col.notNull().unique())
    .addColumn('expires_at', 'timestamp', (col) => col.notNull())
    .addColumn('used', 'boolean', (col) => col.notNull().defaultTo(false))
    .addColumn('used_at', 'timestamp')
    .execute()

  // Add foreign key constraint
  await db.schema
    .createIndex('idx_password_reset_codes_employee_id')
    .on('auth.password_reset_codes')
    .column('employee_id')
    .execute()

  // Add index on reset_code for fast lookups
  await db.schema
    .createIndex('idx_password_reset_codes_reset_code')
    .on('auth.password_reset_codes')
    .column('reset_code')
    .execute()

  // Add index on expires_at for cleanup
  await db.schema
    .createIndex('idx_password_reset_codes_expires_at')
    .on('auth.password_reset_codes')
    .column('expires_at')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('auth.password_reset_codes').execute()
}
