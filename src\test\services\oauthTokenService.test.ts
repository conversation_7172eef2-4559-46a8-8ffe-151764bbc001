import { Employee } from '@models/employees'
import { OAuthClient } from '@models/oauth_clients'
import { oauthTokenService } from '@services/oauthTokenService'
import {
  cleanupTestEmployees,
  cleanupTestOAuthClients,
  createTestEmployee,
  createTestOAuthClient,
} from '@test/setup'
import { afterEach, beforeEach, describe, expect, it } from 'vitest'

describe('OAuthTokenService', () => {
  let testEmployee: Employee
  let testClient: OAuthClient

  beforeEach(async () => {
    // Create test data
    testEmployee = await createTestEmployee({
      first_name: 'Token',
      last_name: 'User',
      email: '<EMAIL>',
    })

    testClient = await createTestOAuthClient({
      client_name: 'Token Test Client',
      client_id: 'token-test-client',
      client_secret: 'token-test-secret',
    })
  })

  afterEach(async () => {
    // Clean up test data
    await cleanupTestEmployees()
    await cleanupTestOAuthClients()
  })

  describe('Token Creation', () => {
    it('should create access and refresh tokens', async () => {
      const tokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      expect(tokens).toHaveProperty('access_token')
      expect(tokens).toHaveProperty('refresh_token')
      expect(tokens).toHaveProperty('token_type', 'Bearer')
      expect(tokens).toHaveProperty('expires_in')
      expect(typeof tokens.access_token).toBe('string')
      expect(typeof tokens.refresh_token).toBe('string')
      expect(tokens.access_token.length).toBeGreaterThan(0)
      expect(tokens.refresh_token.length).toBeGreaterThan(0)
    })

    it('should create tokens with correct expiry', async () => {
      const tokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      expect(tokens.expires_in).toBe(3600) // 1 hour default
    })
  })

  describe('Token Validation', () => {
    it('should validate valid access token', async () => {
      const tokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      const payload = await oauthTokenService.validateAccessToken(
        tokens.access_token
      )

      expect(payload).not.toBeNull()
      expect(payload?.employee_id).toBe(testEmployee.employee_id)
      expect(payload?.client_id).toBe('token-test-client')
      expect(payload?.scope).toBe('read write')
    })

    it('should reject invalid access token', async () => {
      const payload =
        await oauthTokenService.validateAccessToken('invalid.token.here')
      expect(payload).toBeNull()
    })

    it('should reject malformed token', async () => {
      const payload =
        await oauthTokenService.validateAccessToken('not-a-jwt-token')
      expect(payload).toBeNull()
    })
  })

  describe('Authorization Code', () => {
    it('should create authorization code', async () => {
      const code = await oauthTokenService.createAuthorizationCode({
        client_id: 'token-test-client',
        employee: testEmployee,
        redirect_uri: 'http://localhost:3000/callback',
        scope: 'read write',
      })

      expect(typeof code).toBe('string')
      expect(code.length).toBeGreaterThan(0)
    })

    it('should validate and exchange authorization code for tokens', async () => {
      const code = await oauthTokenService.createAuthorizationCode({
        client_id: 'token-test-client',
        employee: testEmployee,
        redirect_uri: 'http://localhost:3000/callback',
        scope: 'read write',
      })

      const tokens = await oauthTokenService.exchangeAuthorizationCode(
        code,
        'token-test-client',
        'http://localhost:3000/callback'
      )

      expect(tokens).not.toBeNull()
      expect(tokens).toHaveProperty('access_token')
      expect(tokens).toHaveProperty('refresh_token')
      expect(tokens).toHaveProperty('token_type', 'Bearer')
    })

    it('should reject invalid authorization code', async () => {
      const tokens = await oauthTokenService.exchangeAuthorizationCode(
        'invalid-code',
        'token-test-client',
        'http://localhost:3000/callback'
      )

      expect(tokens).toBeNull()
    })

    it('should reject code with wrong redirect URI', async () => {
      const code = await oauthTokenService.createAuthorizationCode({
        client_id: 'token-test-client',
        employee: testEmployee,
        redirect_uri: 'http://localhost:3000/callback',
        scope: 'read write',
      })

      const tokens = await oauthTokenService.exchangeAuthorizationCode(
        code,
        'token-test-client',
        'http://localhost:3000/wrong-callback'
      )

      expect(tokens).toBeNull()
    })
  })

  describe('Refresh Token', () => {
    it('should refresh access token with valid refresh token', async () => {
      const originalTokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      const newTokens = await oauthTokenService.refreshAccessToken(
        originalTokens.refresh_token
      )

      expect(newTokens).not.toBeNull()
      expect(newTokens).toHaveProperty('access_token')
      expect(newTokens).toHaveProperty('refresh_token')
      expect(newTokens?.access_token).not.toBe(originalTokens.access_token)
      expect(newTokens?.employee.employee_id).toBe(testEmployee.employee_id)
    })

    it('should reject invalid refresh token', async () => {
      const newTokens = await oauthTokenService.refreshAccessToken(
        'invalid-refresh-token'
      )

      expect(newTokens).toBeNull()
    })
  })

  describe('Token Revocation', () => {
    it('should revoke access token', async () => {
      const tokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      const revoked = await oauthTokenService.revokeToken(
        tokens.access_token,
        'token-test-client'
      )

      expect(revoked).toBe(true)

      // Token should no longer be valid
      const payload = await oauthTokenService.validateAccessToken(
        tokens.access_token
      )
      expect(payload).toBeNull()
    })

    it('should revoke refresh token', async () => {
      const tokens = await oauthTokenService.createTokens({
        client_id: 'token-test-client',
        employee: testEmployee,
        scope: 'read write',
      })

      const revoked = await oauthTokenService.revokeToken(
        tokens.refresh_token,
        'token-test-client'
      )

      expect(revoked).toBe(true)

      // Refresh token should no longer work
      const newTokens = await oauthTokenService.refreshAccessToken(
        tokens.refresh_token
      )
      expect(newTokens).toBeNull()
    })
  })

  describe('Algorithm and Keys', () => {
    it('should return correct algorithm', () => {
      const algorithm = oauthTokenService.getAlgorithm()
      expect(algorithm).toBe('RS256')
    })

    it('should return public key', () => {
      const publicKey = oauthTokenService.getPublicKey()
      expect(publicKey).not.toBeNull()
      expect(typeof publicKey).toBe('string')
      expect(publicKey?.length).toBeGreaterThan(0)
      expect(publicKey).toContain('BEGIN PUBLIC KEY')
    })
  })
})
