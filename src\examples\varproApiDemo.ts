#!/usr/bin/env node

import { config } from 'dotenv'
import { varProApiClient, VarProApiClient } from '@librarys/varproApi'

// Load environment variables
config({ path: __dirname + `/../../.env` })

/**
 * VarPro API Demo
 * Demonstrates how to use the VarPro API library
 */

async function main() {
  console.log('VarPro API Demo')
  console.log('===============')
  console.log(`API URL: ${varProApiClient.getBaseUrl()}`)
  console.log()

  try {
    // 1. Test API connection
    console.log('1. Testing API connection...')
    const isConnected = await varProApiClient.testConnection()
    console.log(`   Connection: ${isConnected ? '✅ Success' : '❌ Failed'}`)

    if (!isConnected) {
      console.log('   Cannot proceed without API connection.')
      return
    }
    console.log()

    // 2. Fetch first page of employees
    console.log('2. Fetching first page of employees (limit 5)...')
    const firstPage = await varProApiClient.getEmployeeList({
      page: 1,
      limit: 5,
    })
    console.log(`   Status: ${firstPage.ok ? '✅ Success' : '❌ Failed'}`)
    console.log(`   Employees on this page: ${firstPage.data.length}`)
    console.log(`   Total employees: ${firstPage.totalItems}`)
    console.log(`   Total pages: ${firstPage.totalPages}`)
    console.log(`   Current page: ${firstPage.currentPage}`)
    console.log(`   Limit: ${firstPage.limit}`)
    console.log()

    // 3. Show sample employee data
    if (firstPage.data.length > 0) {
      console.log('3. Sample employee data:')
      const sampleEmployee = firstPage.data[0]
      console.log(`   Employee ID: ${sampleEmployee.employee_id}`)
      console.log(
        `   Name: ${sampleEmployee.first_name} ${sampleEmployee.last_name}`
      )
      console.log(`   Department: ${sampleEmployee.department || 'N/A'}`)
      console.log(`   Status: ${sampleEmployee.status}`)
      console.log(`   Email: ${sampleEmployee.email || 'N/A'}`)
      console.log(`   Employment Date: ${sampleEmployee.employment_date}`)
      console.log(`   Barcode: ${sampleEmployee.barcode || 'N/A'}`)
      console.log(
        `   Badge Barcode (emp_barcode): ${sampleEmployee.emp_barcode || 'N/A'}`
      )
      console.log(`   RFID Code: ${sampleEmployee.rfid_code || 'N/A'}`)
      console.log(`   Created: ${sampleEmployee.created_at}`)
      console.log(`   Updated: ${sampleEmployee.updated_at}`)
      console.log()
    }

    // 4. Test pagination
    console.log('4. Testing pagination...')
    if (firstPage.totalPages > 1) {
      console.log(`   Fetching page 2...`)
      const secondPage = await varProApiClient.getEmployeeList({
        page: 2,
        limit: 5,
      })
      console.log(`   Page 2 employees: ${secondPage.data.length}`)
      console.log(`   Current page: ${secondPage.currentPage}`)
    } else {
      console.log(`   Only 1 page available, skipping pagination test.`)
    }
    console.log()

    // 5. Test limit validation
    console.log('5. Testing limit validation...')
    try {
      await varProApiClient.getEmployeeList({ page: 1, limit: 300 }) // Should fail
      console.log('   ❌ Limit validation failed - should have thrown error')
    } catch (error) {
      console.log(
        `   ✅ Limit validation working: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
    console.log()

    // 6. Demonstrate field mapping
    console.log('6. Field mapping demonstration:')
    console.log('   VarPro API field -> Local DB field')
    console.log('   emp_barcode -> badge_barcode')
    console.log('   barcode (string) -> barcode (number)')
    console.log('   created_at -> vpmysql_created_at')
    console.log('   updated_at -> vpmysql_updated_at')
    console.log()

    // 7. Show API capabilities
    console.log('7. API Capabilities:')
    console.log('   ✅ Pagination support (page, limit)')
    console.log('   ✅ Limit validation (max 250)')
    console.log('   ✅ Error handling and logging')
    console.log('   ✅ Connection testing')
    console.log('   ✅ Fetch all employees (getAllEmployees)')
    console.log('   ✅ TypeScript interfaces for type safety')
    console.log('   ✅ Configurable base URL via environment')
    console.log()

    // 8. Performance test (optional)
    const shouldRunPerformanceTest = process.argv.includes('--performance')
    if (shouldRunPerformanceTest) {
      console.log('8. Performance test (fetching 50 employees)...')
      const startTime = Date.now()
      const performanceTest = await varProApiClient.getEmployeeList({
        page: 1,
        limit: 50,
      })
      const endTime = Date.now()
      const duration = endTime - startTime
      console.log(
        `   Fetched ${performanceTest.data.length} employees in ${duration}ms`
      )
      console.log(
        `   Average: ${(duration / performanceTest.data.length).toFixed(2)}ms per employee`
      )
      console.log()
    }

    console.log('✅ Demo completed successfully!')
    console.log()
    console.log('Next steps:')
    console.log('- Run sync: npm run cli:varpro-sync sync')
    console.log('- Check stats: npm run cli:varpro-sync stats')
    console.log('- Test connection: npm run cli:varpro-sync test')
  } catch (error) {
    console.error(
      '❌ Demo failed:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    console.error('Stack trace:', error)
  }
}

// Additional utility functions for demonstration

/**
 * Create a custom VarPro API client with different configuration
 */
function createCustomClient() {
  console.log('\nCustom Client Example:')
  console.log('======================')

  // You can create custom instances for different environments
  const customClient = new VarProApiClient()
  console.log(`Custom client base URL: ${customClient.getBaseUrl()}`)

  // The singleton instance is also available
  console.log(`Singleton client base URL: ${varProApiClient.getBaseUrl()}`)
}

/**
 * Show environment configuration
 */
function showEnvironmentConfig() {
  console.log('\nEnvironment Configuration:')
  console.log('==========================')
  console.log(
    `VARPRO_API_URL: ${process.env.VARPRO_API_URL || 'Not set (using default)'}`
  )
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'Not set'}`)
  console.log()
}

// Run the demo
if (require.main === module) {
  // Show environment first
  showEnvironmentConfig()

  // Show custom client example
  createCustomClient()

  // Run main demo
  main().catch(console.error)
}
