import { defineConfig } from 'kysely-ctl'
import { db } from './src/db/database'

export default defineConfig({
  // replace me with a real dialect instance OR a dialect name + `dialectConfig` prop.
  kysely: db,
  migrations: {
    migrationFolder: '/src/db/migrations',
    getMigrationPrefix: () => {
      // use iso 8601 date format for migration files
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getUTCMonth() + 1).padStart(2, '0')
      const day = String(date.getUTCDate()).padStart(2, '0')
      const hours = String(date.getUTCHours()).padStart(2, '0')
      const minutes = String(date.getUTCMinutes()).padStart(2, '0')
      const seconds = String(date.getUTCSeconds()).padStart(2, '0')
      const formattedDate = `${year}${month}${day}${hours}${minutes}${seconds}`
      return `${formattedDate}-`
    },
    migrationTableName: 'auth.migration',
    migrationLockTableName: 'auth.migration_lock',
  },
  //   plugins: [],
  //   seeds: {
  //     seedFolder: "seeds",
  //   }
})
