#!/usr/bin/env node

import { varProApiClient } from '@librarys/varproApi'
import { varProSyncService } from '@services/varproSyncService'
import { config } from 'dotenv'

// Load environment variables
config({ path: __dirname + `/../../.env` })

/**
 * VarPro Sync CLI Tool
 * Provides command-line interface for VarPro API synchronization
 */

async function main() {
  const args = process.argv.slice(2)
  const command = args[0]

  console.log('VarPro Sync CLI Tool')
  console.log('===================')

  try {
    switch (command) {
      case 'test':
        await testConnection()
        break
      case 'sync':
        await runSync()
        break
      case 'stats':
        await showStats()
        break
      case 'help':
      case '--help':
      case '-h':
        showHelp()
        break
      default:
        console.log('Unknown command. Use "help" to see available commands.')
        showHelp()
        process.exit(1)
    }
  } catch (error) {
    console.error(
      'Error:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    process.exit(1)
  }
}

/**
 * Test VarPro API connection
 */
async function testConnection() {
  console.log('Testing VarPro API connection...')
  console.log(`API URL: ${varProApiClient.getBaseUrl()}`)

  try {
    const isConnected = await varProApiClient.testConnection()

    if (isConnected) {
      console.log('✅ Connection successful!')

      // Try to fetch a small sample
      console.log('\nFetching sample data...')
      const sampleResponse = await varProApiClient.getEmployeeList({
        page: 1,
        limit: 5,
      })
      console.log(
        `📊 Sample response: ${sampleResponse.data.length} employees on page 1`
      )
      console.log(`📈 Total employees available: ${sampleResponse.totalItems}`)
      console.log(`📄 Total pages: ${sampleResponse.totalPages}`)
    } else {
      console.log('❌ Connection failed!')
      process.exit(1)
    }
  } catch (error) {
    console.error(
      '❌ Connection test failed:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    process.exit(1)
  }
}

/**
 * Run full employee synchronization
 */
async function runSync() {
  console.log('Starting VarPro employee synchronization...')

  const startTime = Date.now()

  try {
    const result = await varProSyncService.syncAllEmployees()

    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)

    console.log('\n📊 Sync Results:')
    console.log(`✅ Created: ${result.created} employees`)
    console.log(`🔄 Updated: ${result.updated} employees`)
    console.log(`❌ Errors: ${result.errors.length}`)
    console.log(`⏱️  Duration: ${duration} seconds`)

    if (result.errors.length > 0) {
      console.log('\n❌ Errors encountered:')
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`)
      })
    }

    console.log('\n✅ Synchronization completed!')
  } catch (error) {
    console.error(
      '❌ Synchronization failed:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    process.exit(1)
  }
}

/**
 * Show synchronization statistics
 */
async function showStats() {
  console.log('Fetching synchronization statistics...')

  try {
    const stats = await varProSyncService.getSyncStats()

    console.log('\n📊 Sync Statistics:')
    console.log(`👥 Total employees in database: ${stats.totalEmployees}`)
    console.log(
      `🕐 Last sync time: ${stats.lastSyncTime ? stats.lastSyncTime.toLocaleString() : 'Never'}`
    )

    // Test API connection for current status
    console.log('\n🔗 API Status:')
    const isConnected = await varProApiClient.testConnection()
    console.log(
      `📡 VarPro API: ${isConnected ? '✅ Connected' : '❌ Disconnected'}`
    )

    if (isConnected) {
      try {
        const apiStats = await varProApiClient.getEmployeeList({
          page: 1,
          limit: 1,
        })
        console.log(`📈 Total employees in VarPro API: ${apiStats.totalItems}`)
      } catch (error) {
        console.log('⚠️  Could not fetch API employee count')
      }
    }
  } catch (error) {
    console.error(
      '❌ Failed to fetch statistics:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    process.exit(1)
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
Usage: npx tsx src/cli/varpro-sync.ts <command>

Commands:
  test     Test VarPro API connection and fetch sample data
  sync     Run full employee synchronization from VarPro API
  stats    Show synchronization statistics and current status
  help     Show this help message

Examples:
  npx tsx src/cli/varpro-sync.ts test
  npx tsx src/cli/varpro-sync.ts sync
  npx tsx src/cli/varpro-sync.ts stats

Environment Variables:
  VARPRO_API_URL    VarPro API base URL (default: http://localhost:5000)

Notes:
  - The sync command will create new employees and update existing ones
  - OAuth-related fields (password_hash, username) are not synced from VarPro
  - The emp_barcode field from VarPro maps to badge_barcode in the local database
  - Sync preserves existing OAuth authentication data for employees
`)
}

// Run the CLI
if (require.main === module) {
  main()
}
