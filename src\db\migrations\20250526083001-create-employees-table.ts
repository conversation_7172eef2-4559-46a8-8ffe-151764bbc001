import { sql, type <PERSON>ysely } from 'kysely'

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('auth.employees')
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('employee_id', 'integer', (col) => col.notNull().unique())
    .addColumn('employment_date', 'date', (col) => col.notNull())
    .addColumn('status', 'boolean', (col) => col.notNull())
    .addColumn('email', 'varchar(255)', (col) => col.unique())
    .addColumn('username', 'varchar(255)', (col) => col.unique())
    .addColumn('password_hash', 'varchar(255)')
    .addColumn('pin', 'varchar(8)')
    .addColumn('first_name', 'varchar(255)')
    .addColumn('last_name', 'varchar(255)')
    .addColumn('department', 'varchar(255)')
    .addColumn('short_name', 'varchar(255)')
    .addColumn('nickname', 'varchar(255)')
    .addColumn('last_login_at', 'timestamp')
    .addColumn('failed_login_attempts', 'integer', (col) =>
      col.notNull().defaultTo(0)
    )
    .addColumn('locked_until', 'timestamp')
    .addColumn('is_admin', 'boolean', (col) => col.notNull().defaultTo(false))
    .addColumn('work_area_id', 'integer')
    .addColumn('dui', 'varchar(255)')
    .addColumn('nit', 'varchar(255)')
    .addColumn('isss', 'varchar(255)')
    .addColumn('image', 'varchar(255)')
    .addColumn('employee_contact', 'varchar(255)')
    .addColumn('emergency_number', 'varchar(255)')
    .addColumn('gender', 'varchar(255)')
    .addColumn('barcode', 'integer', (col) => col.unique())
    .addColumn('badge_barcode', 'varchar(100)', (col) => col.unique())
    .addColumn('rfid_code', 'integer')
    .addColumn('section', 'varchar(255)')
    .addColumn('title', 'varchar(255)')
    .addColumn('departments_id', 'integer')
    .addColumn('title_id', 'integer')
    .addColumn('vpmysql_created_at', 'timestamp')
    .addColumn('vpmysql_updated_at', 'timestamp')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('auth.employees').execute()
}
