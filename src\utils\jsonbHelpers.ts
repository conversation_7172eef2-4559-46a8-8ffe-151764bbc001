/**
 * Generic helper to stringify JSONB fields in any object
 * @param data - Object that may contain JSONB fields
 * @param jsonbFields - Array of field names that should be stringified
 * @returns Object with specified fields stringified
 */
export function stringifyJsonbFields<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  T extends Record<string, any>,
  K extends keyof T,
>(
  data: T,
  jsonbFields: K[]
): {
  [L in keyof T]: L extends K ? string : T[L]
} {
  const result = { ...data }

  for (const field of jsonbFields) {
    if (result[field] !== undefined && result[field] !== null) {
      if (Array.isArray(result[field]) || typeof result[field] === 'object') {
        // @ts-expect-error Purposely converting to a string
        result[field] = JSON.stringify(result[field])
      }
    }
  }

  return result
}

/**
 * Type guard to check if a value needs JSON stringification
 * @param value - Value to check
 * @returns True if value should be stringified for JSONB storage
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function needsStringification(value: any): boolean {
  return (
    value !== null &&
    value !== undefined &&
    (Array.isArray(value) ||
      (typeof value === 'object' && typeof value !== 'string'))
  )
}

/**
 * Safely stringify a value for JSONB storage
 * @param value - Value to stringify
 * @returns JSON string or null
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function safeJsonStringify(value: any): string | null {
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'string') {
    // If it's already a string, check if it's valid JSON
    try {
      JSON.parse(value)
      return value // Already valid JSON string
    } catch {
      // Not valid JSON, stringify it
      return JSON.stringify(value)
    }
  }

  return JSON.stringify(value)
}
