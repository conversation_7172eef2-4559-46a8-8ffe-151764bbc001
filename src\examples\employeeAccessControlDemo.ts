/**
 * Employee Access Control Demonstration
 *
 * This example shows how the employee controller implements access control:
 * - Employees can only access their own data
 * - Admin employees can access any employee data
 * - All operations require JWT authentication (except password reset)
 */

import { Request, Response } from 'express'

// Example JWT token payloads for different user types

// Regular employee JWT payload
const regularEmployeeToken = {
  employee_id: 123,
  client_id: 'varpro-client',
  scope: 'read write',
  employee: {
    employee_id: 123,
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    nickname: 'johndo<PERSON>',
    is_admin: false, // ❌ Not an admin
  },
}

// Admin employee JWT payload
const adminEmployeeToken = {
  employee_id: 456,
  client_id: 'varpro-admin',
  scope: 'read write admin',
  employee: {
    employee_id: 456,
    first_name: '<PERSON>',
    last_name: 'Ad<PERSON>',
    nickname: 'janeadmin',
    is_admin: true, // ✅ Is an admin
  },
}

/**
 * Access Control Rules Demonstration
 */

// ✅ ALLOWED: Regular employee accessing their own data
function regularEmployeeAccessingOwnData() {
  console.log('📋 Scenario: Regular employee accessing their own data')

  // GET /api/v1/employees/123 with employee_id: 123 in JWT
  const request = {
    params: { id: '123' },
    headers: { authorization: 'Bearer jwt_token_for_employee_123' },
  }

  console.log('✅ ALLOWED: Employee 123 can view their own profile')
  console.log('✅ ALLOWED: Employee 123 can update their own username/password')
  console.log('✅ ALLOWED: Employee 123 can change their own password')
  console.log('✅ ALLOWED: Employee 123 can remove their own OAuth access')
  console.log('')
}

// ❌ FORBIDDEN: Regular employee accessing another employee's data
function regularEmployeeAccessingOtherData() {
  console.log(
    "📋 Scenario: Regular employee trying to access another employee's data"
  )

  // GET /api/v1/employees/999 with employee_id: 123 in JWT
  const request = {
    params: { id: '999' }, // Different employee ID
    headers: { authorization: 'Bearer jwt_token_for_employee_123' },
  }

  console.log("❌ FORBIDDEN: Employee 123 cannot view employee 999's profile")
  console.log("❌ FORBIDDEN: Employee 123 cannot update employee 999's data")
  console.log(
    "❌ FORBIDDEN: Employee 123 cannot change employee 999's password"
  )
  console.log(
    "❌ FORBIDDEN: Employee 123 cannot remove employee 999's OAuth access"
  )
  console.log(
    'Response: 403 Forbidden - "You can only access your own employee data"'
  )
  console.log('')
}

// ✅ ALLOWED: Admin employee accessing any employee's data
function adminEmployeeAccessingAnyData() {
  console.log("📋 Scenario: Admin employee accessing any employee's data")

  // GET /api/v1/employees/999 with admin employee_id: 456 in JWT
  const request = {
    params: { id: '999' }, // Different employee ID
    headers: { authorization: 'Bearer jwt_token_for_admin_456' },
  }

  console.log("✅ ALLOWED: Admin 456 can view any employee's profile")
  console.log("✅ ALLOWED: Admin 456 can update any employee's data")
  console.log("✅ ALLOWED: Admin 456 can change any employee's password")
  console.log("✅ ALLOWED: Admin 456 can remove any employee's OAuth access")
  console.log('✅ ALLOWED: Admin 456 can view all employees list')
  console.log('')
}

// ❌ FORBIDDEN: Regular employee accessing admin-only endpoints
function regularEmployeeAccessingAdminEndpoints() {
  console.log(
    '📋 Scenario: Regular employee trying to access admin-only endpoints'
  )

  // GET /api/v1/employees (list all employees) with regular employee JWT
  const request = {
    headers: { authorization: 'Bearer jwt_token_for_employee_123' },
  }

  console.log('❌ FORBIDDEN: Employee 123 cannot list all employees')
  console.log('Response: 403 Forbidden - "Admin access required"')
  console.log('')
}

/**
 * Middleware Chain Example
 */
function middlewareChainExample() {
  console.log('🔧 Middleware Chain for Employee Endpoints:')
  console.log('')

  console.log('1. jwtHeaderAuthMiddleware:')
  console.log('   - Validates JWT token signature')
  console.log('   - Extracts employee_id from token')
  console.log('   - Sets res.locals.tokenPayload')
  console.log('')

  console.log('2. Controller Access Control:')
  console.log('   - Checks if employee_id matches target resource')
  console.log('   - If not, checks if employee is admin')
  console.log('   - Allows/denies access based on rules')
  console.log('')

  console.log('3. jwtAdminAuthMiddleware (for admin-only endpoints):')
  console.log('   - Additional check for is_admin flag')
  console.log('   - Used for GET /employees (list all)')
  console.log('')
}

/**
 * API Endpoint Access Matrix
 */
function accessMatrix() {
  console.log('📊 Employee API Access Matrix:')
  console.log('')
  console.log(
    '| Endpoint                          | Regular Employee | Admin Employee |'
  )
  console.log(
    '|-----------------------------------|------------------|----------------|'
  )
  console.log(
    '| GET /employees/me                 | ✅ Own data      | ✅ Own data    |'
  )
  console.log(
    '| GET /employees                    | ❌ Forbidden     | ✅ All data    |'
  )
  console.log(
    '| GET /employees/:id                | ✅ Own only      | ✅ Any ID      |'
  )
  console.log(
    '| PUT /employees/:id                | ✅ Own only      | ✅ Any ID      |'
  )
  console.log(
    '| DELETE /employees/:id             | ✅ Own only      | ✅ Any ID      |'
  )
  console.log(
    '| POST /employees/:id/change-password| ✅ Own only      | ✅ Any ID      |'
  )
  console.log(
    '| POST /employees/setup-oauth       | ✅ Own only      | ✅ Any ID      |'
  )
  console.log(
    '| POST /employees/reset-password    | ✅ No auth req   | ✅ No auth req |'
  )
  console.log('')
}

/**
 * Security Features
 */
function securityFeatures() {
  console.log('🔒 Security Features Implemented:')
  console.log('')
  console.log('✅ JWT Token Validation:')
  console.log('   - RS256 signature verification')
  console.log('   - Token expiration checking')
  console.log('   - Employee ID extraction')
  console.log('')

  console.log('✅ Role-Based Access Control:')
  console.log('   - Regular employees: own data only')
  console.log('   - Admin employees: all data access')
  console.log('   - is_admin flag validation')
  console.log('')

  console.log('✅ Resource-Level Authorization:')
  console.log('   - Employee ID matching for self-access')
  console.log('   - Admin override for cross-employee access')
  console.log('   - Proper error messages for forbidden access')
  console.log('')

  console.log('✅ Defense in Depth:')
  console.log('   - Middleware-level authentication')
  console.log('   - Controller-level authorization')
  console.log('   - Database-level validation')
  console.log('')
}

/**
 * Example Error Responses
 */
function errorResponseExamples() {
  console.log('❌ Example Error Responses:')
  console.log('')

  console.log('401 Unauthorized (missing/invalid token):')
  console.log(
    JSON.stringify(
      {
        error: 'unauthorized',
        message: 'Authentication required',
      },
      null,
      2
    )
  )
  console.log('')

  console.log('403 Forbidden (accessing other employee data):')
  console.log(
    JSON.stringify(
      {
        error: 'forbidden',
        message:
          'You can only access your own employee data. Admin access required for other employees.',
      },
      null,
      2
    )
  )
  console.log('')

  console.log('403 Forbidden (admin-only endpoint):')
  console.log(
    JSON.stringify(
      {
        error: 'forbidden',
        message: 'Admin access required',
      },
      null,
      2
    )
  )
  console.log('')
}

/**
 * Main demonstration function
 */
export function demonstrateEmployeeAccessControl() {
  console.log('🚀 Employee Access Control Demonstration\n')

  regularEmployeeAccessingOwnData()
  regularEmployeeAccessingOtherData()
  adminEmployeeAccessingAnyData()
  regularEmployeeAccessingAdminEndpoints()

  middlewareChainExample()
  accessMatrix()
  securityFeatures()
  errorResponseExamples()

  console.log('✅ Employee access control demonstration completed!')
}

// Export for testing
export { regularEmployeeToken, adminEmployeeToken }
