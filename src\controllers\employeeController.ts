import { employeeService } from '@services/employeeService'
import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'

interface AuthenticatedRequest extends Request {
  employee?: {
    employee_id: number
    client_id: string
    scope?: string
    is_admin?: boolean
  }
}

interface AuthenticatedResponse extends Response {
  locals: {
    tokenPayload?: {
      employee_id: number
      client_id: string
      scope?: string
      employee?: {
        employee_id: number
        first_name?: string
        last_name?: string
        nickname?: string
        is_admin?: boolean
      }
    }
    employee?: {
      employee_id: number
      first_name?: string
      last_name?: string
      nickname?: string
      is_admin?: boolean
    }
  }
}

export const getCurrentEmployee = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    // Employee ID comes from JWT middleware
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    const employee = await employeeService.getEmployeeById(
      tokenPayload.employee_id
    )
    if (!employee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(employee)
  } catch (error) {
    console.error('Error getting current employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve current employee',
    })
  }
}

export const getAllEmployees = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    // This endpoint requires admin access - middleware should handle this
    // but we'll double-check here for security
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check if admin flag is in JWT or fetch from database
    let isAdmin = tokenPayload.employee?.is_admin
    if (isAdmin === undefined) {
      const employee = await employeeService.getEmployeeById(
        tokenPayload.employee_id
      )
      isAdmin = employee?.is_admin || false
    }

    if (!isAdmin) {
      res.status(StatusCodes.FORBIDDEN).json({
        error: 'forbidden',
        message: 'Admin access required to view all employees',
      })
      return
    }

    const employees = await employeeService.getAllEmployees()
    res.json(employees)
  } catch (error) {
    console.error('Error getting employees:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve employees',
    })
  }
}

export const getEmployeeById = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // Check authentication
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check access: can only view own data unless admin
    if (tokenPayload.employee_id !== id) {
      // Check if admin
      let isAdmin = tokenPayload.employee?.is_admin
      if (isAdmin === undefined) {
        const authenticatedEmployee = await employeeService.getEmployeeById(
          tokenPayload.employee_id
        )
        isAdmin = authenticatedEmployee?.is_admin || false
      }

      if (!isAdmin) {
        res.status(StatusCodes.FORBIDDEN).json({
          error: 'forbidden',
          message:
            'You can only view your own employee data. Admin access required for other employees.',
        })
        return
      }
    }

    const employee = await employeeService.getEmployeeById(id)
    if (!employee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(employee)
  } catch (error) {
    console.error('Error getting employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve employee',
    })
  }
}

export const setupOAuthAccess = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    const { employee_id, username, password } = req.body

    if (!employee_id || !password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Employee ID and password are required',
      })
      return
    }

    // Check authentication
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check access: can only setup OAuth for own account unless admin
    const targetEmployeeId = parseInt(employee_id)
    if (tokenPayload.employee_id !== targetEmployeeId) {
      // Check if admin
      let isAdmin = tokenPayload.employee?.is_admin
      if (isAdmin === undefined) {
        const authenticatedEmployee = await employeeService.getEmployeeById(
          tokenPayload.employee_id
        )
        isAdmin = authenticatedEmployee?.is_admin || false
      }

      if (!isAdmin) {
        res.status(StatusCodes.FORBIDDEN).json({
          error: 'forbidden',
          message:
            'You can only setup OAuth access for your own account. Admin access required for other employees.',
        })
        return
      }
    }

    // Check if employee exists (must be created by HR/employee management system)
    const existingEmployee =
      await employeeService.getEmployeeById(targetEmployeeId)
    if (!existingEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message:
          'Employee not found. Employee must be created by HR system first.',
      })
      return
    }

    // Set up OAuth access: password and optional username
    await employeeService.updateEmployeePassword(targetEmployeeId, password)
    if (username) {
      const usernameResult = await employeeService.setUsername(
        targetEmployeeId,
        username
      )
      if (!usernameResult.success) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: usernameResult.error || 'Failed to set username',
        })
        return
      }
    }

    const updatedEmployee =
      await employeeService.getEmployeeById(targetEmployeeId)
    res.status(StatusCodes.CREATED).json({
      message: 'OAuth access configured successfully',
      employee: updatedEmployee,
    })
  } catch (error) {
    console.error('Error setting up employee OAuth access:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to set up employee OAuth access',
    })
  }
}

export const updateEmployee = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // Check authentication
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check access: can only update own data unless admin
    if (tokenPayload.employee_id !== id) {
      // Check if admin
      let isAdmin = tokenPayload.employee?.is_admin
      if (isAdmin === undefined) {
        const authenticatedEmployee = await employeeService.getEmployeeById(
          tokenPayload.employee_id
        )
        isAdmin = authenticatedEmployee?.is_admin || false
      }

      if (!isAdmin) {
        res.status(StatusCodes.FORBIDDEN).json({
          error: 'forbidden',
          message:
            'You can only update your own employee data. Admin access required for other employees.',
        })
        return
      }
    }

    const { username, password } = req.body

    // Check if employee exists
    const existingEmployee = await employeeService.getEmployeeById(id)
    if (!existingEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    // Update username if provided
    if (username) {
      const usernameResult = await employeeService.setUsername(id, username)
      if (!usernameResult.success) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: usernameResult.error || 'Failed to update username',
        })
        return
      }
    }

    // Update password if provided
    if (password) {
      await employeeService.updateEmployeePassword(id, password)
    }

    const updatedEmployee = await employeeService.getEmployeeById(id)
    if (!updatedEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(updatedEmployee)
  } catch (error) {
    console.error('Error updating employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to update employee',
    })
  }
}

export const removeOAuthAccess = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // Check authentication
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check access: can only remove OAuth access for own account unless admin
    if (tokenPayload.employee_id !== id) {
      // Check if admin
      let isAdmin = tokenPayload.employee?.is_admin
      if (isAdmin === undefined) {
        const authenticatedEmployee = await employeeService.getEmployeeById(
          tokenPayload.employee_id
        )
        isAdmin = authenticatedEmployee?.is_admin || false
      }

      if (!isAdmin) {
        res.status(StatusCodes.FORBIDDEN).json({
          error: 'forbidden',
          message:
            'You can only remove OAuth access for your own account. Admin access required for other employees.',
        })
        return
      }
    }

    // For OAuth server, we don't actually delete employees, just remove OAuth access
    // Clear password and username to disable OAuth access
    await employeeService.updateEmployee(id, {
      password_hash: null,
      username: null,
    })

    res.status(StatusCodes.NO_CONTENT).send()
  } catch (error) {
    console.error('Error removing employee OAuth access:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to remove employee OAuth access',
    })
  }
}

export const changePassword = async (
  req: AuthenticatedRequest,
  res: AuthenticatedResponse
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // Check authentication
    const tokenPayload = res.locals.tokenPayload
    if (!tokenPayload || !tokenPayload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Authentication required',
      })
      return
    }

    // Check access: can only change own password unless admin
    if (tokenPayload.employee_id !== id) {
      // Check if admin
      let isAdmin = tokenPayload.employee?.is_admin
      if (isAdmin === undefined) {
        const authenticatedEmployee = await employeeService.getEmployeeById(
          tokenPayload.employee_id
        )
        isAdmin = authenticatedEmployee?.is_admin || false
      }

      if (!isAdmin) {
        res.status(StatusCodes.FORBIDDEN).json({
          error: 'forbidden',
          message:
            'You can only change your own password. Admin access required for other employees.',
        })
        return
      }
    }

    const { current_password, new_password } = req.body

    if (!current_password || !new_password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Current password and new password are required',
      })
      return
    }

    const success = await employeeService.changePassword(
      id,
      current_password,
      new_password
    )
    if (!success) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid current password or employee not found',
      })
      return
    }

    res.json({ message: 'Password changed successfully' })
  } catch (error) {
    console.error('Error changing password:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to change password',
    })
  }
}

export const resetPasswordWithCode = async (
  req: Request,
  res: Response
): Promise<void> => {
  // Note: This endpoint does NOT require authentication since it's for forgotten passwords
  // However, it validates that the reset code belongs to the specified employee_id
  try {
    const { reset_code, employee_id, new_password } = req.body

    if (!reset_code || !employee_id || !new_password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Reset code, employee ID, and new password are required',
      })
      return
    }

    // Validate reset code format (UUIDv4)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(reset_code)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid reset code format',
      })
      return
    }

    // Validate employee_id
    const employeeIdNum = parseInt(employee_id)
    if (isNaN(employeeIdNum) || employeeIdNum <= 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // Validate password length
    if (new_password.length < 6) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Password must be at least 6 characters long',
      })
      return
    }

    const result = await employeeService.resetPasswordWithCode(
      reset_code,
      employeeIdNum,
      new_password
    )
    if (!result.success) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: result.error || 'Failed to reset password',
      })
      return
    }

    res.json({ message: 'Password reset successfully' })
  } catch (error) {
    console.error('Error resetting password with code:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to reset password',
    })
  }
}
