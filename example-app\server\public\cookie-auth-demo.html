<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HTTP-Only <PERSON><PERSON> Demo</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-sizing: border-box;
      }
      button {
        background: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin-right: 10px;
      }
      button:hover {
        background: #0056b3;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .logout-btn {
        background: #dc3545;
      }
      .logout-btn:hover {
        background: #c82333;
      }
      .status {
        margin: 20px 0;
        padding: 15px;
        border-radius: 5px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .employee-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }
      .hidden {
        display: none;
      }
      pre {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🍪 HTTP-Only Cookie Authentication Demo</h1>
      <p>
        This demo shows how to use HTTP-only cookies for secure JWT storage
        while still providing employee information to the frontend.
      </p>

      <!-- Login Form -->
      <div id="loginForm">
        <h2>Login</h2>
        <div class="form-group">
          <label for="username">Employee ID, Username, or Email:</label>
          <input
            type="text"
            id="username"
            value="1"
            placeholder="Enter employee ID, username, or email"
          />
        </div>
        <div class="form-group">
          <label for="password">Password or PIN:</label>
          <input
            type="password"
            id="password"
            value="admin123"
            placeholder="Enter password or PIN"
          />
        </div>
        <div class="form-group">
          <label for="clientId">Client ID:</label>
          <input
            type="text"
            id="clientId"
            value="07b2f434-ec60-4cac-aa53-f17008ab5e6d"
          />
        </div>
        <div class="form-group">
          <label for="clientSecret">Client Secret:</label>
          <input
            type="text"
            id="clientSecret"
            value="6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa"
          />
        </div>
        <button onclick="login()">Login</button>
      </div>

      <!-- Authenticated Section -->
      <div id="authenticatedSection" class="hidden">
        <h2>Welcome!</h2>
        <div id="employeeInfo" class="employee-info"></div>
        <button onclick="checkAuth()">Refresh Status</button>
        <button onclick="getProfile()">Get Profile</button>
        <button onclick="getDashboard()">Get Dashboard</button>
        <button onclick="logout()" class="logout-btn">Logout</button>
      </div>

      <!-- Status Messages -->
      <div id="status"></div>

      <!-- Response Display -->
      <div id="response"></div>
    </div>

    <script>
      const API_BASE = '/api'

      // Check authentication status on page load
      window.onload = function () {
        checkAuth()
      }

      async function login() {
        const username = document.getElementById('username').value
        const password = document.getElementById('password').value
        const client_id = document.getElementById('clientId').value
        const client_secret = document.getElementById('clientSecret').value

        try {
          showStatus('Logging in...', 'info')

          const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Important for cookies
            body: JSON.stringify({
              username,
              password,
              client_id,
              client_secret,
            }),
          })

          const data = await response.json()

          if (response.ok) {
            showStatus(
              'Login successful! JWT token stored in HTTP-only cookie.',
              'success'
            )
            showResponse(data)
            updateUI(data)
          } else {
            showStatus(`Login failed: ${data.message}`, 'error')
            showResponse(data)
          }
        } catch (error) {
          showStatus(`Login error: ${error.message}`, 'error')
        }
      }

      async function checkAuth() {
        try {
          const response = await fetch(`${API_BASE}/auth/me`, {
            credentials: 'include', // Important for cookies
          })

          const data = await response.json()
          updateUI(data)
          showResponse(data)

          if (data.authenticated) {
            showStatus('Authenticated via HTTP-only cookie', 'success')
          } else {
            showStatus('Not authenticated', 'info')
          }
        } catch (error) {
          showStatus(`Auth check error: ${error.message}`, 'error')
        }
      }

      async function getProfile() {
        try {
          const response = await fetch(`${API_BASE}/profile`, {
            credentials: 'include',
          })

          const data = await response.json()
          showResponse(data)

          if (response.ok) {
            showStatus('Profile retrieved successfully', 'success')
          } else {
            showStatus(`Profile error: ${data.message}`, 'error')
          }
        } catch (error) {
          showStatus(`Profile error: ${error.message}`, 'error')
        }
      }

      async function getDashboard() {
        try {
          const response = await fetch(`${API_BASE}/dashboard`, {
            credentials: 'include',
          })

          const data = await response.json()
          showResponse(data)

          if (response.ok) {
            showStatus('Dashboard retrieved successfully', 'success')
          } else {
            showStatus(`Dashboard error: ${data.message}`, 'error')
          }
        } catch (error) {
          showStatus(`Dashboard error: ${error.message}`, 'error')
        }
      }

      async function logout() {
        try {
          const response = await fetch(`${API_BASE}/auth/logout`, {
            method: 'POST',
            credentials: 'include',
          })

          const data = await response.json()
          showResponse(data)

          if (response.ok) {
            showStatus(
              'Logout successful! HTTP-only cookie cleared.',
              'success'
            )
            updateUI({ authenticated: false, employee: null })
          } else {
            showStatus(`Logout error: ${data.message}`, 'error')
          }
        } catch (error) {
          showStatus(`Logout error: ${error.message}`, 'error')
        }
      }

      function updateUI(authData) {
        const loginForm = document.getElementById('loginForm')
        const authenticatedSection = document.getElementById(
          'authenticatedSection'
        )
        const employeeInfo = document.getElementById('employeeInfo')

        if (authData.authenticated && authData.employee) {
          loginForm.classList.add('hidden')
          authenticatedSection.classList.remove('hidden')

          employeeInfo.innerHTML = `
                    <h3>Employee Information</h3>
                    <p><strong>Name:</strong> ${authData.employee.full_name}</p>
                    <p><strong>Display Name:</strong> ${authData.employee.display_name}</p>
                    <p><strong>Employee ID:</strong> ${authData.employee.employee_id}</p>
                    <p><strong>First Name:</strong> ${authData.employee.first_name}</p>
                    <p><strong>Last Name:</strong> ${authData.employee.last_name}</p>
                    <p><strong>Nickname:</strong> ${authData.employee.nickname || 'None'}</p>
                    ${authData.token_expires_at ? `<p><strong>Token Expires:</strong> ${new Date(authData.token_expires_at).toLocaleString()}</p>` : ''}
                `
        } else {
          loginForm.classList.remove('hidden')
          authenticatedSection.classList.add('hidden')
          employeeInfo.innerHTML = ''
        }
      }

      function showStatus(message, type) {
        const status = document.getElementById('status')
        status.innerHTML = `<div class="${type}">${message}</div>`
      }

      function showResponse(data) {
        const response = document.getElementById('response')
        response.innerHTML = `
                <h3>API Response:</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `
      }
    </script>
  </body>
</html>
