import { db } from '@db/database'
import { Employee, NewEmployee, EmployeeUpdate } from '@models/employees'
import { varProApiClient, VarProEmployee } from '@librarys/varproApi'

/**
 * VarPro Sync Service
 * Handles synchronization between VarPro API and local employees table
 */
export class VarProSyncService {
  /**
   * Sync all employees from VarPro API to local database
   * This method fetches all employees from VarPro API and syncs them with the local database
   * @returns Promise<{ created: number; updated: number; errors: string[] }>
   */
  async syncAllEmployees(): Promise<{
    created: number
    updated: number
    errors: string[]
  }> {
    console.log('Starting VarPro employee sync...')

    const result = {
      created: 0,
      updated: 0,
      errors: [] as string[],
    }

    try {
      // Test API connection first
      const isConnected = await varProApiClient.testConnection()
      if (!isConnected) {
        throw new Error('Failed to connect to VarPro API')
      }

      // Fetch all employees from VarPro API
      console.log('Fetching employees from VarPro API...')
      const varProEmployees = await varProApiClient.getAllEmployees()
      console.log(`Fetched ${varProEmployees.length} employees from VarPro API`)

      // Process each employee
      for (const varProEmployee of varProEmployees) {
        try {
          await this.syncSingleEmployee(varProEmployee)

          // Check if employee was created or updated
          const existingEmployee = await this.getLocalEmployee(
            varProEmployee.employee_id
          )
          if (existingEmployee) {
            result.updated++
          } else {
            result.created++
          }
        } catch (error) {
          const errorMessage = `Failed to sync employee ${varProEmployee.employee_id}: ${error instanceof Error ? error.message : 'Unknown error'}`
          console.error(errorMessage)
          result.errors.push(errorMessage)
        }
      }

      console.log(
        `VarPro sync completed: ${result.created} created, ${result.updated} updated, ${result.errors.length} errors`
      )
      return result
    } catch (error) {
      const errorMessage = `VarPro sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      console.error(errorMessage)
      result.errors.push(errorMessage)
      return result
    }
  }

  /**
   * Sync a single employee from VarPro API to local database
   * @param varProEmployee VarPro employee data
   * @returns Promise<Employee>
   */
  async syncSingleEmployee(varProEmployee: VarProEmployee): Promise<Employee> {
    try {
      // Check if employee exists in local database
      const existingEmployee = await this.getLocalEmployee(
        varProEmployee.employee_id
      )

      if (existingEmployee) {
        // Update existing employee
        return await this.updateLocalEmployee(varProEmployee, existingEmployee)
      } else {
        // Create new employee
        return await this.createLocalEmployee(varProEmployee)
      }
    } catch (error) {
      console.error(
        `Error syncing employee ${varProEmployee.employee_id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Get local employee by employee_id
   * @param employeeId Employee ID
   * @returns Promise<Employee | null>
   */
  private async getLocalEmployee(employeeId: number): Promise<Employee | null> {
    try {
      const employee = await db
        .selectFrom('auth.employees')
        .selectAll()
        .where('employee_id', '=', employeeId)
        .executeTakeFirst()

      return employee || null
    } catch (error) {
      console.error(`Error fetching local employee ${employeeId}:`, error)
      throw error
    }
  }

  /**
   * Create new local employee from VarPro data
   * @param varProEmployee VarPro employee data
   * @returns Promise<Employee>
   */
  private async createLocalEmployee(
    varProEmployee: VarProEmployee
  ): Promise<Employee> {
    try {
      const newEmployee: NewEmployee = this.mapVarProToLocal(varProEmployee)

      const result = await db
        .insertInto('auth.employees')
        .values(newEmployee)
        .returningAll()
        .executeTakeFirstOrThrow()

      console.log(`Created new employee: ${varProEmployee.employee_id}`)
      return result
    } catch (error) {
      console.error(
        `Error creating employee ${varProEmployee.employee_id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Update existing local employee with VarPro data
   * @param varProEmployee VarPro employee data
   * @param existingEmployee Existing local employee
   * @returns Promise<Employee>
   */
  private async updateLocalEmployee(
    varProEmployee: VarProEmployee,
    existingEmployee: Employee
  ): Promise<Employee> {
    try {
      const updateData: EmployeeUpdate =
        this.mapVarProToLocalUpdate(varProEmployee)

      // Only update if there are actual changes
      const hasChanges = this.hasEmployeeChanges(
        varProEmployee,
        existingEmployee
      )
      if (!hasChanges) {
        console.log(`No changes for employee: ${varProEmployee.employee_id}`)
        return existingEmployee
      }

      const result = await db
        .updateTable('auth.employees')
        .set({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .where('employee_id', '=', varProEmployee.employee_id)
        .returningAll()
        .executeTakeFirstOrThrow()

      console.log(`Updated employee: ${varProEmployee.employee_id}`)
      return result
    } catch (error) {
      console.error(
        `Error updating employee ${varProEmployee.employee_id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Map VarPro employee data to local employee format for creation
   * @param varProEmployee VarPro employee data
   * @returns NewEmployee
   */
  private mapVarProToLocal(varProEmployee: VarProEmployee): NewEmployee {
    return {
      employee_id: varProEmployee.employee_id,
      status: varProEmployee.status,
      first_name: varProEmployee.first_name,
      last_name: varProEmployee.last_name,
      department: varProEmployee.department,
      short_name: varProEmployee.short_name,
      nickname: varProEmployee.nickname,
      work_area_id: varProEmployee.work_area_id,
      dui: varProEmployee.dui,
      nit: varProEmployee.nit,
      isss: varProEmployee.isss,
      employment_date: varProEmployee.employment_date,
      image: varProEmployee.image,
      employee_contact: varProEmployee.employee_contact,
      emergency_number: varProEmployee.emergency_number,
      gender: varProEmployee.gender,
      barcode: varProEmployee.barcode ? parseInt(varProEmployee.barcode) : null,
      badge_barcode: varProEmployee.emp_barcode, // Map emp_barcode to badge_barcode
      rfid_code: varProEmployee.rfid_code,
      section: varProEmployee.section,
      title: varProEmployee.title,
      departments_id: varProEmployee.departments_id,
      title_id: varProEmployee.title_id,
      email: varProEmployee.email,
      // Don't sync OAuth-related fields from VarPro
      password_hash: null,
      username: null,
      // Set VarPro sync timestamps
      vpmysql_created_at: varProEmployee.created_at,
      vpmysql_updated_at: varProEmployee.updated_at,
    }
  }

  /**
   * Map VarPro employee data to local employee format for updates
   * @param varProEmployee VarPro employee data
   * @returns EmployeeUpdate
   */
  private mapVarProToLocalUpdate(
    varProEmployee: VarProEmployee
  ): EmployeeUpdate {
    return {
      status: varProEmployee.status,
      first_name: varProEmployee.first_name,
      last_name: varProEmployee.last_name,
      department: varProEmployee.department,
      short_name: varProEmployee.short_name,
      nickname: varProEmployee.nickname,
      work_area_id: varProEmployee.work_area_id,
      dui: varProEmployee.dui,
      nit: varProEmployee.nit,
      isss: varProEmployee.isss,
      employment_date: varProEmployee.employment_date,
      image: varProEmployee.image,
      employee_contact: varProEmployee.employee_contact,
      emergency_number: varProEmployee.emergency_number,
      gender: varProEmployee.gender,
      barcode: varProEmployee.barcode ? parseInt(varProEmployee.barcode) : null,
      badge_barcode: varProEmployee.emp_barcode, // Map emp_barcode to badge_barcode
      rfid_code: varProEmployee.rfid_code,
      section: varProEmployee.section,
      title: varProEmployee.title,
      departments_id: varProEmployee.departments_id,
      title_id: varProEmployee.title_id,
      email: varProEmployee.email,
      // Update VarPro sync timestamps
      vpmysql_updated_at: varProEmployee.updated_at,
      // Don't update OAuth-related fields (password_hash, username, etc.)
    }
  }

  /**
   * Check if VarPro employee data has changes compared to local employee
   * @param varProEmployee VarPro employee data
   * @param localEmployee Local employee data
   * @returns boolean
   */
  private hasEmployeeChanges(
    varProEmployee: VarProEmployee,
    localEmployee: Employee
  ): boolean {
    // Compare key fields that might change
    const fieldsToCompare = [
      'status',
      'first_name',
      'last_name',
      'department',
      'short_name',
      'nickname',
      'work_area_id',
      'dui',
      'nit',
      'isss',
      'employment_date',
      'image',
      'employee_contact',
      'emergency_number',
      'gender',
      'rfid_code',
      'section',
      'title',
      'departments_id',
      'title_id',
      'email',
    ]

    for (const field of fieldsToCompare) {
      const varProValue = varProEmployee[field as keyof VarProEmployee]
      const localValue = localEmployee[field as keyof Employee]

      if (varProValue !== localValue) {
        return true
      }
    }

    // Check barcode conversion
    const varProBarcode = varProEmployee.barcode
      ? parseInt(varProEmployee.barcode)
      : null
    if (varProBarcode !== localEmployee.barcode) {
      return true
    }

    // Check badge_barcode mapping
    if (varProEmployee.emp_barcode !== localEmployee.badge_barcode) {
      return true
    }

    // Check VarPro timestamps
    if (varProEmployee.updated_at !== localEmployee.vpmysql_updated_at) {
      return true
    }

    return false
  }

  /**
   * Get sync statistics
   * @returns Promise<{ totalEmployees: number; lastSyncTime: Date | null }>
   */
  async getSyncStats(): Promise<{
    totalEmployees: number
    lastSyncTime: Date | null
  }> {
    try {
      const result = await db
        .selectFrom('auth.employees')
        .select([
          db.fn.count('employee_id').as('total'),
          db.fn.max('updated_at').as('lastSync'),
        ])
        .executeTakeFirst()

      return {
        totalEmployees: Number(result?.total || 0),
        lastSyncTime: result?.lastSync ? new Date(result.lastSync) : null,
      }
    } catch (error) {
      console.error('Error getting sync stats:', error)
      throw error
    }
  }
}

// Export singleton instance
export const varProSyncService = new VarProSyncService()
