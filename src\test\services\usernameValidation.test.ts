import { employeeService } from '@services/employeeService'
import { describe, expect, it } from 'vitest'

describe('Username Validation', () => {
  describe('validateUsername method', () => {
    // Access the private method through reflection for testing
    const validateUsername = (employeeService as any).validateUsername.bind(
      employeeService
    )

    it('should accept valid usernames with letters and numbers', () => {
      const result = validateUsername('validuser123')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with underscores and letters', () => {
      const result = validateUsername('user_name')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with dashes and letters', () => {
      const result = validateUsername('user-name')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept single letter usernames', () => {
      const result = validateUsername('a')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept mixed case usernames with numbers', () => {
      const result = validateUsername('A1')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with allowed special characters', () => {
      const result = validateUsername('user!name')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with dots', () => {
      const result = validateUsername('john.doe')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject usernames containing @ character', () => {
      const result = validateUsername('<EMAIL>')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject usernames with only numbers', () => {
      const result = validateUsername('12345')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe(
        'Username must contain at least 1 alphabetic character'
      )
    })

    it('should reject usernames starting with @', () => {
      const result = validateUsername('@username')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject usernames ending with @', () => {
      const result = validateUsername('username@')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject usernames with @ in the middle', () => {
      const result = validateUsername('user@name')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject empty usernames', () => {
      const result = validateUsername('')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot be empty')
    })

    it('should reject whitespace-only usernames', () => {
      const result = validateUsername('   ')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot be empty')
    })

    it('should reject usernames with only tabs', () => {
      const result = validateUsername('\t\t\t')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot be empty')
    })

    it('should reject special characters with @ symbol', () => {
      const result = validateUsername('!@#$%')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should accept special characters without @ symbol', () => {
      const result = validateUsername('user!#$%name')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with numbers and special characters', () => {
      const result = validateUsername('user123!#$')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject usernames with only special characters (no letters)', () => {
      const result = validateUsername('!#$%^&*()')
      expect(result.isValid).toBe(false)
      expect(result.error).toBe(
        'Username must contain at least 1 alphabetic character'
      )
    })

    it('should accept long usernames with letters', () => {
      const result = validateUsername(
        'verylongusernamewithalotofcharactersbuthasletters123'
      )
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with unicode letters', () => {
      const result = validateUsername('userñame')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with accented characters', () => {
      const result = validateUsername('usérnamé')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })
  })

  describe('Business Rules Validation', () => {
    const validateUsername = (employeeService as any).validateUsername.bind(
      employeeService
    )

    it('should enforce the "no @ character" rule consistently', () => {
      const testCases = [
        '<EMAIL>',
        '@username',
        'username@',
        'user@name',
        'test@test@test',
        'a@b',
        '1@2',
      ]

      testCases.forEach((username) => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(false)
        expect(result.error).toBe('Username cannot contain @ character')
      })
    })

    it('should enforce the "at least 1 alphabetic character" rule consistently', () => {
      const testCases = [
        '12345',
        '!#$%^&*()',
        '999',
        '0',
        '123.456',
        '___',
        '---',
      ]

      testCases.forEach((username) => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(false)
        expect(result.error).toBe(
          'Username must contain at least 1 alphabetic character'
        )
      })
    })

    it('should check @ character before alphabetic character rule', () => {
      // Test cases that contain @ character should fail with @ error, not alphabetic error
      const testCases = ['123!@#', '1@2', '!@#']

      testCases.forEach((username) => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(false)
        expect(result.error).toBe('Username cannot contain @ character')
      })
    })

    it('should accept valid usernames that meet all criteria', () => {
      const testCases = [
        'a',
        'user123',
        'john.doe',
        'user_name',
        'user-name',
        'User123',
        'ADMIN',
        'test1',
        'a1',
        'employee_001',
        'admin-user',
        'user.name.123',
      ]

      testCases.forEach((username) => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(true)
        expect(result.error).toBeUndefined()
      })
    })
  })
})
