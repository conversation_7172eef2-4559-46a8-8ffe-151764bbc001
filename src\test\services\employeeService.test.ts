import { Employee } from '@models/employees'
import { employeeService } from '@services/employeeService'
import { cleanupTestEmployees, createTestEmployee } from '@test/setup'
import { afterEach, beforeEach, describe, expect, it } from 'vitest'

describe('EmployeeService', () => {
  let testEmployee: Employee

  beforeEach(async () => {
    // Create a test employee for each test
    testEmployee = await createTestEmployee({
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
    })
  })

  afterEach(async () => {
    // Clean up test data
    await cleanupTestEmployees()
  })

  describe('Username Validation', () => {
    it('should accept valid usernames with letters and numbers', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'validuser123'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with underscores and letters', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'user_name'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with dashes and letters', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'user-name'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept single letter usernames', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'a'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept mixed case usernames with numbers', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'A1'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept usernames with allowed special characters', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'user!name'
      )
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject usernames containing @ character', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        '<EMAIL>'
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject usernames with only numbers', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        '12345'
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe(
        'Username must contain at least 1 alphabetic character'
      )
    })

    it('should reject usernames starting with @', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        '@username'
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject usernames ending with @', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        'username@'
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })

    it('should reject empty usernames', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        ''
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot be empty')
    })

    it('should reject whitespace-only usernames', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        '   '
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot be empty')
    })

    it('should reject special characters with @ symbol', async () => {
      const result = await employeeService.setUsername(
        testEmployee.employee_id,
        '!@#$%'
      )
      expect(result.success).toBe(false)
      expect(result.error).toBe('Username cannot contain @ character')
    })
  })

  describe('Username Uniqueness', () => {
    it('should reject duplicate usernames', async () => {
      // Set username for first employee
      const firstResult = await employeeService.setUsername(
        testEmployee.employee_id,
        'uniqueuser'
      )
      expect(firstResult.success).toBe(true)

      // Create second employee
      const secondEmployee = await createTestEmployee({
        first_name: 'Jane',
        last_name: 'Smith',
        email: '<EMAIL>',
      })

      // Try to set same username for second employee
      const secondResult = await employeeService.setUsername(
        secondEmployee.employee_id,
        'uniqueuser'
      )
      expect(secondResult.success).toBe(false)
      expect(secondResult.error).toBe('Username already exists')
    })

    it('should allow updating username to the same value', async () => {
      // Set initial username
      const firstResult = await employeeService.setUsername(
        testEmployee.employee_id,
        'sameuser'
      )
      expect(firstResult.success).toBe(true)

      // Update to same username should succeed
      const secondResult = await employeeService.setUsername(
        testEmployee.employee_id,
        'sameuser'
      )
      expect(secondResult.success).toBe(true)
    })
  })

  describe('Password Operations', () => {
    it('should update employee password successfully', async () => {
      const result = await employeeService.updateEmployeePassword(
        testEmployee.employee_id,
        'newPassword123'
      )
      expect(result).toBe(true)
    })

    it('should validate employee credentials correctly', async () => {
      // Set username and password
      await employeeService.setUsername(testEmployee.employee_id, 'testuser')
      await employeeService.updateEmployeePassword(
        testEmployee.employee_id,
        'testPassword123'
      )

      // Validate credentials
      const result = await employeeService.validateEmployee(
        'testuser',
        'testPassword123'
      )
      expect(result).not.toBeNull()
      expect(result?.employee_id).toBe(testEmployee.employee_id)
    })

    it('should reject invalid credentials', async () => {
      // Set username and password
      await employeeService.setUsername(testEmployee.employee_id, 'testuser')
      await employeeService.updateEmployeePassword(
        testEmployee.employee_id,
        'testPassword123'
      )

      // Try with wrong password
      const result = await employeeService.validateEmployee(
        'testuser',
        'wrongPassword'
      )
      expect(result).toBeNull()
    })
  })

  describe('Employee Retrieval', () => {
    it('should get employee by ID', async () => {
      const employee = await employeeService.getEmployeeById(
        testEmployee.employee_id
      )
      expect(employee).not.toBeUndefined()
      expect(employee?.employee_id).toBe(testEmployee.employee_id)
      expect(employee?.first_name).toBe('John')
      expect(employee?.last_name).toBe('Doe')
    })

    it('should get employee by username', async () => {
      await employeeService.setUsername(testEmployee.employee_id, 'johntest')

      const employee = await employeeService.getEmployeeByUsername('johntest')
      expect(employee).not.toBeUndefined()
      expect(employee?.employee_id).toBe(testEmployee.employee_id)
      expect(employee?.username).toBe('johntest')
    })

    it('should get employee by email', async () => {
      const employee =
        await employeeService.getEmployeeByEmail('<EMAIL>')
      expect(employee).not.toBeUndefined()
      expect(employee?.employee_id).toBe(testEmployee.employee_id)
      expect(employee?.email).toBe('<EMAIL>')
    })

    it('should return undefined for non-existent employee', async () => {
      const employee = await employeeService.getEmployeeById(99999)
      expect(employee).toBeUndefined()
    })
  })
})
