import {
  ColumnType,
  Generated,
  Insertable,
  Selectable,
  Updateable,
} from 'kysely'

export interface OAuthTokensTable {
  id: Generated<number>
  access_token: string
  refresh_token: string | null
  token_type: string // 'Bearer'
  expires_at: ColumnType<Date, string | undefined, string | undefined>
  scope: string | null
  client_id: string
  employee_id: number // For password grant type
  authorization_code: string | null // For authorization code flow
  code_challenge: string | null // For PKCE
  code_challenge_method: string | null // 'S256' or 'plain'
  redirect_uri: string | null
  is_revoked: boolean
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

// Export types for OAuth Tokens
export type OAuthToken = Selectable<OAuthTokensTable>
export type NewOAuthToken = Insertable<OAuthTokensTable>
export type OAuthTokenUpdate = Updateable<OAuthTokensTable>
