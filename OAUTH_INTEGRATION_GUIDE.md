# OAuth Authorization Server Integration Guide

This guide explains how to integrate your Express/React application with the OAuth 2.0 Authorization Server to provide secure employee authentication and authorization.

## 📋 Overview

The OAuth Authorization Server provides:

- **Employee Authentication**: Login with employee_id/email/username + password or PIN
- **JWT Tokens**: RS256-signed tokens with employee information embedded
- **Role-Based Access**: Regular employees vs admin employees
- **Secure Sessions**: HTTP-only cookies for web apps, Bearer tokens for APIs
- **Password Management**: Change password, reset with codes

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │  Express API    │    │  OAuth Server   │
│                 │    │                 │    │                 │
│ - Login Form    │◄──►│ - Auth Routes   │◄──►│ - JWT Tokens    │
│ - Protected UI  │    │ - Middleware    │    │ - Employee Data │
│ - HTTP Cookies  │    │ - API Endpoints │    │ - Admin Roles   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Implementation Requirements

### Backend (Express.js)

#### 1. Dependencies

```bash
npm install axios jose cookie-parser cors
```

#### 2. Environment Variables

```env
# OAuth Server Configuration
OAUTH_SERVER_URL=http://localhost:3230
JWT_ISSUER=oauth2-auth-server
JWKS_CACHE_TTL=3600000

# Client Credentials (stored securely on server only)
OAUTH_CLIENT_ID=your-client-id
OAUTH_CLIENT_SECRET=your-client-secret
OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback

# Session Configuration
SESSION_SECRET=your-session-secret
COOKIE_DOMAIN=localhost
COOKIE_SECURE=false
```

#### 3. Required Middleware

- **Authentication Middleware**: Validates JWT tokens using RS256 public keys
- **Employee Middleware**: Extracts employee information from JWT tokens
- **Admin Middleware**: Checks for admin privileges
- **CORS Middleware**: Handles cross-origin requests with credentials

#### 4. Required Routes

```typescript
// Authentication routes
POST /auth/login          // Login with credentials
POST /auth/logout         // Clear session cookies
GET  /auth/me             // Get current user info
GET  /auth/callback       // OAuth callback (if using authorization code flow)

// Protected API routes
GET  /api/profile         // Employee profile (requires auth)
GET  /api/dashboard       // Dashboard data (requires auth)
GET  /api/admin/*         // Admin endpoints (requires admin role)
```

### Frontend (React)

#### 1. Dependencies

```bash
npm install axios
```

#### 2. Required Components

- **Login Form**: Username/password input with validation
- **Protected Routes**: Route wrapper that checks authentication
- **User Context**: Global state for current user information
- **Admin Guards**: Components that check admin privileges

#### 3. Required Features

- **Automatic Token Refresh**: Handle token expiration gracefully
- **Role-Based UI**: Show/hide features based on user role
- **Error Handling**: Display authentication errors clearly
- **Logout Functionality**: Clear session and redirect to login

## 🔐 Authentication Flows

### 1. Password Grant Flow (Recommended for Internal Apps)

```
1. User enters credentials in React app
2. React sends credentials to Express backend
3. Express exchanges credentials for JWT token with OAuth server
4. Express sets HTTP-only cookie with JWT token
5. React receives success response (no token in response body)
6. Subsequent requests include cookie automatically
```

### 2. Authorization Code Flow (For External Apps)

```
1. User clicks "Login" in React app
2. React redirects to OAuth server authorization endpoint
3. User enters credentials on OAuth server
4. OAuth server redirects back with authorization code
5. Express backend exchanges code for JWT token
6. Express sets HTTP-only cookie and redirects to app
```

## 📝 Implementation Checklist

### Backend Implementation

- [ ] Install required dependencies
- [ ] Configure environment variables
- [ ] Create authentication middleware that:
  - [ ] Validates JWT signatures using JWKS
  - [ ] Extracts employee information from tokens
  - [ ] Handles token expiration
  - [ ] Sets proper CORS headers
- [ ] Create authentication routes:
  - [ ] POST /auth/login (password grant)
  - [ ] POST /auth/logout (clear cookies)
  - [ ] GET /auth/me (current user info)
- [ ] Implement role-based access control:
  - [ ] Regular employee access
  - [ ] Admin employee access
  - [ ] Proper error responses
- [ ] Add protected API endpoints with middleware

### Frontend Implementation

- [ ] Create login form component with:
  - [ ] Username/email field (accepts employee_id, email, or username)
  - [ ] Password field (accepts password or 4-8 digit PIN)
  - [ ] Form validation
  - [ ] Error display
- [ ] Implement authentication context:
  - [ ] User state management
  - [ ] Login/logout functions
  - [ ] Auto-refresh user info
- [ ] Create protected route wrapper
- [ ] Add role-based UI components:
  - [ ] Admin-only sections
  - [ ] Employee-specific features
- [ ] Implement automatic authentication checking
- [ ] Add logout functionality

## 🔑 Key Features to Implement

### 1. Employee Information Access

```typescript
// JWT token contains employee information
{
  employee_id: 123,
  employee: {
    employee_id: 123,
    first_name: "John",
    last_name: "Doe",
    nickname: "johndoe",
    is_admin: false
  },
  client_id: "your-app",
  scope: "read write",
  exp: 1234567890
}
```

### 2. Role-Based Access Control

```typescript
// Check if user is admin
if (user.employee.is_admin) {
  // Show admin features
} else {
  // Show regular employee features
}
```

### 3. Secure Cookie Configuration

```typescript
// HTTP-only cookies for security
res.cookie('access_token', jwt_token, {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  maxAge: 3600000, // 1 hour
})
```

## 🚨 Security Considerations

### Required Security Measures

- [ ] **HTTP-Only Cookies**: Store JWT tokens in HTTP-only cookies (not localStorage)
- [ ] **HTTPS in Production**: Always use HTTPS in production environments
- [ ] **CORS Configuration**: Properly configure CORS with credentials
- [ ] **Token Validation**: Always validate JWT signatures using public keys
- [ ] **Role Verification**: Double-check admin roles for sensitive operations
- [ ] **Error Handling**: Don't expose sensitive information in error messages

### OAuth Server Security Features

- ✅ **RS256 JWT Signing**: Asymmetric key signing for security
- ✅ **Token Expiration**: Configurable token lifetimes
- ✅ **Employee-Based Auth**: Authentication tied to employee records
- ✅ **Admin Role Support**: Built-in role-based access control
- ✅ **Password Security**: Bcrypt hashing with salt rounds
- ✅ **PIN Support**: 4-8 digit PIN authentication option

## 📚 Example Integration

### Backend Middleware Example

```typescript
// Authentication middleware
app.use('/api', authMiddleware.authenticate())
app.use('/api', authMiddleware.getEmployee())

// Admin-only routes
app.use('/api/admin', authMiddleware.requireAdmin())
```

### Frontend Context Example

```typescript
// User context with employee information
const UserContext = createContext({
  user: null,
  isAuthenticated: false,
  isAdmin: false,
  login: async (credentials) => {},
  logout: async () => {},
})
```

## 🎯 Next Steps

1. **Copy this guide** to your target application repository
2. **Review the requirements** with your development team
3. **Set up OAuth client** credentials with the OAuth server admin
4. **Request implementation** by providing this guide to your developer
5. **Test integration** with both regular and admin employee accounts

## 📞 Support

When requesting implementation, provide:

- [ ] This complete guide
- [ ] Your OAuth server URL and client credentials
- [ ] Your application's current authentication method (if any)
- [ ] Specific role-based features you need
- [ ] Any custom employee data requirements

The OAuth server provides a complete authentication solution with employee management, role-based access control, and secure JWT token handling for modern web applications.

## 💻 Code Examples

### Backend Authentication Middleware

```typescript
import axios from 'axios'
import * as jose from 'jose'

class AuthMiddleware {
  constructor() {
    this.oauthServerUrl = process.env.OAUTH_SERVER_URL
    this.jwtIssuer = process.env.JWT_ISSUER
  }

  // Validate JWT token using OAuth server's public keys
  async verifyToken(token) {
    const jwksUrl = `${this.oauthServerUrl}/.well-known/jwks.json`
    const jwks = await axios.get(jwksUrl).then((res) => res.data)
    const JWKS = jose.createLocalJWKSet(jwks)

    const { payload } = await jose.jwtVerify(token, JWKS, {
      issuer: this.jwtIssuer,
      algorithms: ['RS256'],
    })

    return payload
  }

  // Express middleware for authentication
  authenticate() {
    return async (req, res, next) => {
      try {
        const token = req.cookies?.access_token
        if (!token) {
          return res.status(401).json({ error: 'Authentication required' })
        }

        const payload = await this.verifyToken(token)
        req.tokenPayload = payload
        req.employee = payload.employee
        next()
      } catch (error) {
        res.status(401).json({ error: 'Invalid token' })
      }
    }
  }

  // Require admin access
  requireAdmin() {
    return (req, res, next) => {
      if (!req.employee?.is_admin) {
        return res.status(403).json({ error: 'Admin access required' })
      }
      next()
    }
  }
}

export default new AuthMiddleware()
```

### Frontend Login Component

```typescript
import React, { useState, useContext } from 'react';
import { UserContext } from '../contexts/UserContext';

export const LoginForm = () => {
  const [credentials, setCredentials] = useState({
    username: '', // Can be employee_id, email, or username
    password: ''  // Can be password or PIN
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useContext(UserContext);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(credentials);
      // Redirect handled by context
    } catch (err) {
      setError(err.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <div className="form-group">
        <label>Employee ID, Email, or Username:</label>
        <input
          type="text"
          value={credentials.username}
          onChange={(e) => setCredentials({
            ...credentials,
            username: e.target.value
          })}
          placeholder="Enter employee ID, email, or username"
          required
        />
      </div>

      <div className="form-group">
        <label>Password or PIN:</label>
        <input
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials({
            ...credentials,
            password: e.target.value
          })}
          placeholder="Enter password or 4-8 digit PIN"
          required
        />
      </div>

      {error && <div className="error">{error}</div>}

      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
};
```

### User Context Provider

```typescript
import React, { createContext, useState, useEffect } from 'react';
import axios from 'axios';

export const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Configure axios to include cookies
  axios.defaults.withCredentials = true;

  const login = async (credentials) => {
    const response = await axios.post('/auth/login', credentials);
    await checkAuth(); // Refresh user info
    return response.data;
  };

  const logout = async () => {
    await axios.post('/auth/logout');
    setUser(null);
    setIsAuthenticated(false);
  };

  const checkAuth = async () => {
    try {
      const response = await axios.get('/auth/me');
      setUser(response.data.employee);
      setIsAuthenticated(true);
    } catch (error) {
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  const value = {
    user,
    isAuthenticated,
    isAdmin: user?.is_admin || false,
    loading,
    login,
    logout,
    checkAuth
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};
```

## 🔗 OAuth Server Endpoints

### Authentication Endpoints

```
POST /oauth/token
- Grant types: password, authorization_code, refresh_token
- Returns: JWT access token and refresh token

POST /oauth/revoke
- Revokes access or refresh tokens

GET /.well-known/jwks.json
- Public keys for JWT verification

GET /api/v1/users/me
- Returns current employee information (requires Bearer token)
```

### Employee Management Endpoints

```
GET /api/v1/employees/me
- Get current employee profile

PUT /api/v1/employees/:id
- Update employee (self or admin only)

POST /api/v1/employees/:id/change-password
- Change password (self or admin only)

POST /api/v1/employees/reset-password
- Reset password with code (no auth required)
```

## 🎨 UI/UX Considerations

### Login Form Features

- [ ] Support for multiple login methods (employee_id, email, username)
- [ ] PIN support (4-8 digits) alongside regular passwords
- [ ] Clear validation messages
- [ ] Loading states during authentication
- [ ] Remember login preference (not credentials)

### Protected UI Elements

```typescript
// Role-based component rendering
const AdminPanel = () => {
  const { isAdmin } = useContext(UserContext);

  if (!isAdmin) {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return <div>Admin features here...</div>;
};

// Protected route wrapper
const ProtectedRoute = ({ children, adminOnly = false }) => {
  const { isAuthenticated, isAdmin, loading } = useContext(UserContext);

  if (loading) return <div>Loading...</div>;
  if (!isAuthenticated) return <Navigate to="/login" />;
  if (adminOnly && !isAdmin) return <div>Admin access required</div>;

  return children;
};
```
