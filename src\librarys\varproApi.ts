import axios, { AxiosInstance } from 'axios'
import { config } from 'dotenv'

// Load environment variables
config({ path: __dirname + `/../../.env` })

// VarPro API Employee interface based on the endpoint return structure
export interface VarProEmployee {
  employee_id: number
  status: number
  first_name: string | null
  last_name: string | null
  department: string | null
  short_name: string | null
  nickname: string | null
  work_area_id: number | null
  dui: string | null
  nit: string | null
  isss: string | null
  employment_date: string // ISO date string
  image: string | null
  employee_contact: string | null
  emergency_number: string | null
  gender: string | null
  barcode: string | null
  created_at: string // ISO date string
  updated_at: string // ISO date string
  emp_barcode: number | null // This maps to badge_barcode in our local table
  rfid_code: number | null
  section: string | null
  title: string | null
  departments_id: number | null
  title_id: number | null
  email: string | null
  password_hash: string | null
  username: string | null
}

// VarPro API Response structure for employee list endpoint
export interface VarProEmployeeListResponse {
  ok: boolean
  data: VarProEmployee[]
  totalItems: number
  totalPages: number
  currentPage: number
  limit: number
}

// Query parameters for employee list endpoint
export interface EmployeeListParams {
  page?: number
  limit?: number // Max 250
}

// VarPro API Error interface
export interface VarProApiError {
  ok: false
  error: string
  message?: string
}

/**
 * VarPro API Client
 * Handles communication with the VarPro REST API
 */
export class VarProApiClient {
  private client: AxiosInstance
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.VARPRO_API_URL || 'http://localhost:5000'

    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    })

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(
          `VarPro API Request: ${config.method?.toUpperCase()} ${config.url}`
        )
        return config
      },
      (error) => {
        console.error('VarPro API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(
          `VarPro API Response: ${response.status} ${response.config.url}`
        )
        return response
      },
      (error) => {
        console.error(
          'VarPro API Response Error:',
          error.response?.data || error.message
        )
        return Promise.reject(error)
      }
    )
  }

  /**
   * Get employee list with pagination
   * @param params Query parameters for pagination
   * @returns Promise<VarProEmployeeListResponse>
   */
  async getEmployeeList(
    params: EmployeeListParams = {}
  ): Promise<VarProEmployeeListResponse> {
    try {
      // Validate limit parameter (max 250)
      if (params.limit && params.limit > 250) {
        throw new Error('Limit parameter cannot exceed 250')
      }

      const queryParams = new URLSearchParams()

      if (params.page) {
        queryParams.append('page', params.page.toString())
      }

      if (params.limit) {
        queryParams.append('limit', params.limit.toString())
      }

      const url = `/employee/list${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      const response = await this.client.get<
        VarProEmployeeListResponse | VarProApiError
      >(url)

      if (!response.data.ok) {
        const errorData = response.data as VarProApiError
        throw new Error(
          `VarPro API returned error: ${errorData.error || 'Unknown error'}`
        )
      }

      return response.data
    } catch (error) {
      console.error('Error fetching employee list from VarPro API:', error)
      throw error
    }
  }

  /**
   * Get all employees by fetching all pages
   * @param limit Items per page (max 250)
   * @returns Promise<VarProEmployee[]>
   */
  async getAllEmployees(limit: number = 250): Promise<VarProEmployee[]> {
    try {
      if (limit > 250) {
        throw new Error('Limit parameter cannot exceed 250')
      }

      const allEmployees: VarProEmployee[] = []
      let currentPage = 1
      let totalPages = 1

      do {
        const response = await this.getEmployeeList({
          page: currentPage,
          limit,
        })

        allEmployees.push(...response.data)
        totalPages = response.totalPages
        currentPage++

        console.log(
          `Fetched page ${currentPage - 1} of ${totalPages} (${response.data.length} employees)`
        )

        // Add a small delay to avoid overwhelming the API
        if (currentPage <= totalPages) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      } while (currentPage <= totalPages)

      console.log(
        `Successfully fetched all ${allEmployees.length} employees from VarPro API`
      )
      return allEmployees
    } catch (error) {
      console.error('Error fetching all employees from VarPro API:', error)
      throw error
    }
  }

  /**
   * Test the API connection
   * @returns Promise<boolean>
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getEmployeeList({ page: 1, limit: 1 })
      console.log('VarPro API connection test successful')
      return true
    } catch (error) {
      console.error('VarPro API connection test failed:', error)
      return false
    }
  }

  /**
   * Get API base URL
   * @returns string
   */
  getBaseUrl(): string {
    return this.baseUrl
  }
}

// Export a singleton instance
export const varProApiClient = new VarProApiClient()

// Export the class for testing or custom instances
export default VarProApiClient
