import { describe, it, expect, beforeEach, vi } from 'vitest'
import { VarProSyncService } from '@services/varproSyncService'
import { varProApiClient, VarProEmployee } from '@librarys/varproApi'
import { db } from '@db/database'

// Mock the VarPro API client
vi.mock('@librarys/varproApi', () => ({
  varProApiClient: {
    testConnection: vi.fn(),
    getAllEmployees: vi.fn(),
    getEmployeeList: vi.fn(),
    getBaseUrl: vi.fn(() => 'http://localhost:5000')
  },
  VarProApiClient: vi.fn()
}))

// Mock the database
vi.mock('@db/database', () => ({
  db: {
    selectFrom: vi.fn(() => ({
      selectAll: vi.fn(() => ({
        where: vi.fn(() => ({
          executeTakeFirst: vi.fn()
        }))
      })),
      select: vi.fn(() => ({
        executeTakeFirst: vi.fn()
      }))
    })),
    insertInto: vi.fn(() => ({
      values: vi.fn(() => ({
        returningAll: vi.fn(() => ({
          executeTakeFirstOrThrow: vi.fn()
        }))
      }))
    })),
    updateTable: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returningAll: vi.fn(() => ({
            executeTakeFirstOrThrow: vi.fn()
          }))
        }))
      }))
    })),
    fn: {
      count: vi.fn(() => ({ as: vi.fn() })),
      max: vi.fn(() => ({ as: vi.fn() }))
    }
  }
}))

describe('VarProSyncService', () => {
  let syncService: VarProSyncService
  
  const mockVarProEmployee: VarProEmployee = {
    employee_id: 123,
    status: 1,
    first_name: 'John',
    last_name: 'Doe',
    department: 'IT',
    short_name: null,
    nickname: 'Johnny',
    work_area_id: 1,
    dui: '12345678-9',
    nit: '1234-567890-123-4',
    isss: '123456789',
    employment_date: '2020-01-01T00:00:00.000Z',
    image: null,
    employee_contact: null,
    emergency_number: null,
    gender: 'Male',
    barcode: '000123',
    created_at: '2020-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    emp_barcode: 1000123,
    rfid_code: 12345,
    section: 'Development',
    title: 'Developer',
    departments_id: 1,
    title_id: 1,
    email: '<EMAIL>',
    password_hash: null,
    username: null
  }

  beforeEach(() => {
    syncService = new VarProSyncService()
    vi.clearAllMocks()
  })

  describe('syncAllEmployees', () => {
    it('should successfully sync employees from VarPro API', async () => {
      // Mock API connection test
      vi.mocked(varProApiClient.testConnection).mockResolvedValue(true)
      
      // Mock API response
      vi.mocked(varProApiClient.getAllEmployees).mockResolvedValue([mockVarProEmployee])
      
      // Mock database operations
      const mockSelectFrom = vi.mocked(db.selectFrom)
      mockSelectFrom.mockReturnValue({
        selectAll: vi.fn(() => ({
          where: vi.fn(() => ({
            executeTakeFirst: vi.fn().mockResolvedValue(null) // Employee doesn't exist
          }))
        }))
      } as any)
      
      const mockInsertInto = vi.mocked(db.insertInto)
      mockInsertInto.mockReturnValue({
        values: vi.fn(() => ({
          returningAll: vi.fn(() => ({
            executeTakeFirstOrThrow: vi.fn().mockResolvedValue({
              ...mockVarProEmployee,
              badge_barcode: mockVarProEmployee.emp_barcode
            })
          }))
        }))
      } as any)

      const result = await syncService.syncAllEmployees()

      expect(result.created).toBe(1)
      expect(result.updated).toBe(0)
      expect(result.errors).toHaveLength(0)
      expect(varProApiClient.testConnection).toHaveBeenCalled()
      expect(varProApiClient.getAllEmployees).toHaveBeenCalled()
    })

    it('should handle API connection failure', async () => {
      vi.mocked(varProApiClient.testConnection).mockResolvedValue(false)

      const result = await syncService.syncAllEmployees()

      expect(result.created).toBe(0)
      expect(result.updated).toBe(0)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('Failed to connect to VarPro API')
    })

    it('should handle API errors gracefully', async () => {
      vi.mocked(varProApiClient.testConnection).mockResolvedValue(true)
      vi.mocked(varProApiClient.getAllEmployees).mockRejectedValue(new Error('API Error'))

      const result = await syncService.syncAllEmployees()

      expect(result.created).toBe(0)
      expect(result.updated).toBe(0)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('API Error')
    })
  })

  describe('mapVarProToLocal', () => {
    it('should correctly map VarPro employee to local format', () => {
      const mapped = (syncService as any).mapVarProToLocal(mockVarProEmployee)

      expect(mapped.employee_id).toBe(mockVarProEmployee.employee_id)
      expect(mapped.first_name).toBe(mockVarProEmployee.first_name)
      expect(mapped.last_name).toBe(mockVarProEmployee.last_name)
      expect(mapped.badge_barcode).toBe(mockVarProEmployee.emp_barcode)
      expect(mapped.barcode).toBe(parseInt(mockVarProEmployee.barcode!))
      expect(mapped.password_hash).toBeNull()
      expect(mapped.username).toBeNull()
      expect(mapped.vpmysql_created_at).toBe(mockVarProEmployee.created_at)
      expect(mapped.vpmysql_updated_at).toBe(mockVarProEmployee.updated_at)
    })

    it('should handle null barcode correctly', () => {
      const employeeWithNullBarcode = { ...mockVarProEmployee, barcode: null }
      const mapped = (syncService as any).mapVarProToLocal(employeeWithNullBarcode)

      expect(mapped.barcode).toBeNull()
    })
  })

  describe('hasEmployeeChanges', () => {
    it('should detect changes in employee data', () => {
      const localEmployee = {
        ...mockVarProEmployee,
        badge_barcode: mockVarProEmployee.emp_barcode,
        barcode: parseInt(mockVarProEmployee.barcode!),
        first_name: 'Jane' // Different name
      }

      const hasChanges = (syncService as any).hasEmployeeChanges(mockVarProEmployee, localEmployee)

      expect(hasChanges).toBe(true)
    })

    it('should return false when no changes detected', () => {
      const localEmployee = {
        ...mockVarProEmployee,
        badge_barcode: mockVarProEmployee.emp_barcode,
        barcode: parseInt(mockVarProEmployee.barcode!),
        vpmysql_updated_at: mockVarProEmployee.updated_at
      }

      const hasChanges = (syncService as any).hasEmployeeChanges(mockVarProEmployee, localEmployee)

      expect(hasChanges).toBe(false)
    })

    it('should detect badge_barcode mapping changes', () => {
      const localEmployee = {
        ...mockVarProEmployee,
        badge_barcode: 999999, // Different badge_barcode
        barcode: parseInt(mockVarProEmployee.barcode!),
        vpmysql_updated_at: mockVarProEmployee.updated_at
      }

      const hasChanges = (syncService as any).hasEmployeeChanges(mockVarProEmployee, localEmployee)

      expect(hasChanges).toBe(true)
    })
  })

  describe('getSyncStats', () => {
    it('should return sync statistics', async () => {
      const mockStats = {
        total: '150',
        lastSync: '2023-01-01T12:00:00.000Z'
      }

      const mockSelectFrom = vi.mocked(db.selectFrom)
      mockSelectFrom.mockReturnValue({
        select: vi.fn(() => ({
          executeTakeFirst: vi.fn().mockResolvedValue(mockStats)
        }))
      } as any)

      const stats = await syncService.getSyncStats()

      expect(stats.totalEmployees).toBe(150)
      expect(stats.lastSyncTime).toEqual(new Date('2023-01-01T12:00:00.000Z'))
    })

    it('should handle null stats gracefully', async () => {
      const mockSelectFrom = vi.mocked(db.selectFrom)
      mockSelectFrom.mockReturnValue({
        select: vi.fn(() => ({
          executeTakeFirst: vi.fn().mockResolvedValue(null)
        }))
      } as any)

      const stats = await syncService.getSyncStats()

      expect(stats.totalEmployees).toBe(0)
      expect(stats.lastSyncTime).toBeNull()
    })
  })
})
