import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'

const LoginPage = () => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [loginMethod, setLoginMethod] = useState('password') // 'password' or 'authcode'
  const { login, loginWithAuthCode, loading, error, clearError } = useAuth()

  const handlePasswordLogin = async (e) => {
    e.preventDefault()
    clearError()

    if (!username || !password) {
      return
    }

    await login(username, password)
  }

  const handleAuthCodeLogin = () => {
    clearError()
    loginWithAuthCode()
  }

  return (
    <div className="container">
      <div className="card" style={{ maxWidth: '500px', margin: '40px auto' }}>
        <div className="text-center mb-3">
          <h1>OAuth Client Example</h1>
          <p className="text-muted">
            Demonstrating OAuth 2.0 integration with RS256 JWT tokens
          </p>
        </div>

        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}

        <div className="form-group">
          <label className="form-label">Login Method</label>
          <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
            <button
              type="button"
              className={`btn ${loginMethod === 'password' ? '' : 'btn-secondary'}`}
              onClick={() => setLoginMethod('password')}
            >
              Password Flow
            </button>
            <button
              type="button"
              className={`btn ${loginMethod === 'authcode' ? '' : 'btn-secondary'}`}
              onClick={() => setLoginMethod('authcode')}
            >
              Authorization Code Flow
            </button>
          </div>
        </div>

        {loginMethod === 'password' ? (
          <form onSubmit={handlePasswordLogin}>
            <div className="form-group">
              <label htmlFor="username" className="form-label">
                Employee ID, Username, or Email
              </label>
              <input
                type="text"
                id="username"
                className="form-input"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your employee ID, username, or email"
                disabled={loading}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="password" className="form-label">
                Password or PIN
              </label>
              <input
                type="password"
                id="password"
                className="form-input"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password or 4-8 digit PIN"
                disabled={loading}
                required
              />
            </div>

            <button
              type="submit"
              className="btn"
              style={{ width: '100%' }}
              disabled={loading || !username || !password}
            >
              {loading ? 'Logging in...' : 'Login with Password'}
            </button>
          </form>
        ) : (
          <div>
            <div className="alert alert-info">
              <strong>Authorization Code Flow</strong><br />
              You will be redirected to the OAuth server to authenticate.
              After successful authentication, you'll be redirected back to this application.
            </div>

            <button
              type="button"
              className="btn"
              style={{ width: '100%' }}
              onClick={handleAuthCodeLogin}
              disabled={loading}
            >
              {loading ? 'Redirecting...' : 'Login with Authorization Code'}
            </button>
          </div>
        )}

        <div className="mt-3">
          <div className="alert alert-info">
            <strong>Test Credentials:</strong><br />
            Employee ID: <code>1</code> or Username: <code>william de jesus.rivera marroquin</code><br />
            Password: <code>admin123</code> or PIN: <code>1234</code> (if set)
          </div>
        </div>

        <div className="mt-3 text-center">
          <h3>OAuth 2.0 Features Demonstrated</h3>
          <ul style={{ textAlign: 'left', marginTop: '16px' }}>
            <li>✅ <strong>RS256 JWT Tokens</strong> - Asymmetric cryptography</li>
            <li>✅ <strong>Password Grant Flow</strong> - Direct username/password</li>
            <li>✅ <strong>Authorization Code Flow</strong> - Redirect-based auth</li>
            <li>✅ <strong>Token Verification</strong> - Using public key (JWKS)</li>
            <li>✅ <strong>Automatic Token Refresh</strong> - Seamless re-authentication</li>
            <li>✅ <strong>Protected API Calls</strong> - Bearer token authentication</li>
            <li>✅ <strong>Token Revocation</strong> - Secure logout</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
