import { seedOAuthClients } from './seedOAuthClients'
import { seedTestUser } from './seedTestUser'

async function seedDatabase() {
  console.log('🚀 Starting database seeding...')

  try {
    // Seed OAuth clients first
    await seedOAuthClients()

    // Then seed test user
    await seedTestUser()

    console.log('🎉 Database seeding completed successfully!')
  } catch (error) {
    console.error('💥 Database seeding failed:', error)
    throw error
  }
}

// Run the seeding function if this script is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✨ All seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
}

export { seedDatabase }
